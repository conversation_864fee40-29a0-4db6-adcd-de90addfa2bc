const express = require('express');
const router = express.Router();

/**
 * Valet System Routes Index
 * Consolidates all valet-related routes
 */

// Import valet route modules
const customerRoutes = require('./customer');
const otpRoutes = require('./otp');
const transactionRoutes = require('./transaction');
const qrcodeRoutes = require('./qrcode');

// Mount valet routes
router.use('/customers', customerRoutes);
router.use('/otp', otpRoutes);
router.use('/transactions', transactionRoutes);
router.use('/qrcode', qrcodeRoutes);

// Health check for valet system
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Valet system is running',
    timestamp: new Date(),
    version: '1.0.0'
  });
});

module.exports = router;
