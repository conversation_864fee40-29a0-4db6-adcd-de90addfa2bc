import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Car, QrCode, Search, Phone, Clock, MapPin } from 'lucide-react';
import ValetCustomerFlow from '../components/valet/ValetCustomerFlow';
import TransactionStatus from '../components/valet/TransactionStatus';
import { useToast } from '../hooks/useToast';

const ValetCustomer = () => {
  const [mode, setMode] = useState('home'); // 'home', 'flow', 'track'
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('pnr'); // 'pnr' or 'pin'
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const toast = useToast();

  // Check URL parameters for direct access
  useEffect(() => {
    const pnr = searchParams.get('pnr');
    const pin = searchParams.get('pin');
    const action = searchParams.get('action');

    if (pnr || pin) {
      setMode('track');
      setSearchQuery(pnr || pin);
      setSearchType(pnr ? 'pnr' : 'pin');
    } else if (action === 'start') {
      setMode('flow');
    }
  }, [searchParams]);

  const handleStartValet = () => {
    setMode('flow');
  };

  const handleTrackTransaction = () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter PNR or PIN to track transaction');
      return;
    }
    setMode('track');
  };

  const handleFlowComplete = (flowData) => {
    console.log('Valet flow completed:', flowData);
    toast.success('Valet service completed successfully!');
    setMode('home');
  };

  const handleFlowCancel = () => {
    setMode('home');
  };

  const handleBackToHome = () => {
    setMode('home');
    setSearchQuery('');
    navigate('/valet-customer', { replace: true });
  };

  // Home Screen
  if (mode === 'home') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50">
        
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-4xl mx-auto px-4 py-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Car className="w-8 h-8 text-yellow-600" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Parkwiz Valet</h1>
              <p className="text-gray-600">Premium valet parking service</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-md mx-auto px-4 py-8">
          
          {/* Start Valet Service */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div className="text-center mb-6">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <QrCode className="w-6 h-6 text-yellow-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Start Valet Service</h2>
              <p className="text-gray-600 text-sm">
                Scan the QR code at the valet point to begin
              </p>
            </div>
            
            <button
              onClick={handleStartValet}
              className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
            >
              <QrCode className="w-5 h-5 mr-2" />
              Scan QR Code
            </button>
          </div>

          {/* Track Transaction */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="text-center mb-6">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Search className="w-6 h-6 text-blue-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Track Transaction</h2>
              <p className="text-gray-600 text-sm">
                Enter your PNR or PIN to check status
              </p>
            </div>

            <div className="space-y-4">
              {/* Search Type Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setSearchType('pnr')}
                  className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                    searchType === 'pnr'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  PNR
                </button>
                <button
                  onClick={() => setSearchType('pin')}
                  className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                    searchType === 'pin'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  PIN
                </button>
              </div>

              {/* Search Input */}
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value.toUpperCase())}
                  placeholder={`Enter ${searchType.toUpperCase()}`}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono"
                  maxLength={searchType === 'pnr' ? 10 : 6}
                />
              </div>

              {/* Track Button */}
              <button
                onClick={handleTrackTransaction}
                disabled={!searchQuery.trim()}
                className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Search className="w-5 h-5 mr-2" />
                Track Transaction
              </button>
            </div>
          </div>

          {/* Features */}
          <div className="mt-8 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 text-center">Why Choose Parkwiz Valet?</h3>
            
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center p-4 bg-white rounded-lg shadow-sm">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <Clock className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Quick Service</h4>
                  <p className="text-sm text-gray-600">Fast and efficient valet parking</p>
                </div>
              </div>
              
              <div className="flex items-center p-4 bg-white rounded-lg shadow-sm">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <MapPin className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Secure Parking</h4>
                  <p className="text-sm text-gray-600">Your vehicle is safe with us</p>
                </div>
              </div>
              
              <div className="flex items-center p-4 bg-white rounded-lg shadow-sm">
                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <Phone className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Real-time Updates</h4>
                  <p className="text-sm text-gray-600">Track your vehicle status</p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              © 2024 Parkwiz. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Valet Flow Screen
  if (mode === 'flow') {
    return (
      <ValetCustomerFlow
        onComplete={handleFlowComplete}
        onCancel={handleFlowCancel}
      />
    );
  }

  // Transaction Tracking Screen
  if (mode === 'track') {
    return (
      <div className="min-h-screen bg-gray-50">
        
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-md mx-auto px-4 py-4">
            <div className="flex items-center">
              <button
                onClick={handleBackToHome}
                className="p-2 -ml-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                ← Back
              </button>
              <div className="ml-2">
                <h1 className="text-lg font-semibold text-gray-900">Transaction Status</h1>
                <p className="text-sm text-gray-500">
                  {searchType.toUpperCase()}: {searchQuery}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Transaction Status */}
        <div className="p-4">
          <TransactionStatus
            pnr={searchType === 'pnr' ? searchQuery : null}
            pin={searchType === 'pin' ? searchQuery : null}
            onRefresh={() => {
              console.log('Refreshing transaction');
            }}
          />
          
          {/* Back to Home */}
          <div className="max-w-md mx-auto mt-6 px-6">
            <button
              onClick={handleBackToHome}
              className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default ValetCustomer;
