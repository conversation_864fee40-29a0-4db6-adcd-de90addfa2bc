import React, { useState, useEffect, useRef } from 'react';
import { Shield, ArrowRight, AlertCircle, Loader2, RefreshCw, CheckCircle } from 'lucide-react';
import { valetCustomerApi } from '../../api/valetCustomerApi';
import { useToast } from '../../hooks/useToast';

const OTPVerification = ({ 
  mobileNumber,
  otpId,
  expiresAt,
  onNext, 
  onBack,
  onResend,
  className = "" 
}) => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState('');
  const [timeLeft, setTimeLeft] = useState(0);
  const [isVerified, setIsVerified] = useState(false);
  
  const inputRefs = useRef([]);
  const toast = useToast();

  // Calculate initial time left
  useEffect(() => {
    if (expiresAt) {
      const now = new Date().getTime();
      const expiry = new Date(expiresAt).getTime();
      const remaining = Math.max(0, Math.floor((expiry - now) / 1000));
      setTimeLeft(remaining);
    }
  }, [expiresAt]);

  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  // Auto-focus first input
  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, []);

  const handleOtpChange = (index, value) => {
    // Only allow single digit
    if (value.length > 1) return;
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all fields are filled
    if (newOtp.every(digit => digit !== '') && !isLoading) {
      handleVerifyOTP(newOtp.join(''));
    }
  };

  const handleKeyDown = (index, e) => {
    // Handle backspace
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
    
    // Handle paste
    if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      navigator.clipboard.readText().then(text => {
        const digits = text.replace(/\D/g, '').slice(0, 6);
        if (digits.length === 6) {
          const newOtp = digits.split('');
          setOtp(newOtp);
          handleVerifyOTP(digits);
        }
      });
    }
  };

  const handleVerifyOTP = async (otpValue = null) => {
    const otpToVerify = otpValue || otp.join('');
    
    if (otpToVerify.length !== 6) {
      setError('Please enter complete 6-digit OTP');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const response = await valetCustomerApi.verifyOTP(mobileNumber, otpToVerify);
      
      if (response.success) {
        setIsVerified(true);
        toast.success('OTP verified successfully!');
        
        // Small delay to show success state
        setTimeout(() => {
          onNext({
            mobileNumber,
            otpVerified: true,
            customerId: response.customerId,
            customerData: response.customerData
          });
        }, 1000);
      } else {
        throw new Error(response.message || 'Invalid OTP');
      }
      
    } catch (error) {
      console.error('Error verifying OTP:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Invalid OTP';
      setError(errorMessage);
      toast.error(errorMessage);
      
      // Clear OTP on error
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    try {
      setIsResending(true);
      setError('');
      
      const response = await valetCustomerApi.generateOTP(mobileNumber);
      
      if (response.success) {
        toast.success('New OTP sent successfully!');
        setTimeLeft(300); // Reset to 5 minutes
        setOtp(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
        
        if (onResend) {
          onResend({
            otpId: response.otpId,
            expiresAt: response.expiresAt
          });
        }
      } else {
        throw new Error(response.message || 'Failed to resend OTP');
      }
      
    } catch (error) {
      console.error('Error resending OTP:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to resend OTP';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsResending(false);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const maskedMobile = mobileNumber ? `+91 ${mobileNumber.slice(0, 2)}****${mobileNumber.slice(-2)}` : '';

  return (
    <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
      
      {/* Header */}
      <div className="text-center mb-6">
        <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
          isVerified ? 'bg-green-100' : 'bg-blue-100'
        }`}>
          {isVerified ? (
            <CheckCircle className="w-8 h-8 text-green-600" />
          ) : (
            <Shield className="w-8 h-8 text-blue-600" />
          )}
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {isVerified ? 'Verified!' : 'Verify OTP'}
        </h2>
        <p className="text-gray-600">
          {isVerified 
            ? 'Your mobile number has been verified successfully'
            : `Enter the 6-digit code sent to ${maskedMobile}`
          }
        </p>
      </div>

      {!isVerified && (
        <>
          {/* OTP Input */}
          <div className="mb-6">
            <div className="flex justify-center space-x-3 mb-4">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  ref={el => inputRefs.current[index] = el}
                  type="text"
                  inputMode="numeric"
                  value={digit}
                  onChange={(e) => handleOtpChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  className={`
                    w-12 h-12 text-center text-xl font-bold border-2 rounded-lg
                    focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                    ${error ? 'border-red-300' : digit ? 'border-green-300' : 'border-gray-300'}
                    ${isLoading ? 'bg-gray-50' : 'bg-white'}
                  `}
                  disabled={isLoading}
                  maxLength="1"
                />
              ))}
            </div>
            
            {/* Timer */}
            <div className="text-center">
              {timeLeft > 0 ? (
                <p className="text-sm text-gray-600">
                  Code expires in <span className="font-medium text-blue-600">{formatTime(timeLeft)}</span>
                </p>
              ) : (
                <p className="text-sm text-red-600">OTP has expired</p>
              )}
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          {/* Manual Verify Button (if needed) */}
          {otp.every(digit => digit !== '') && !isLoading && (
            <button
              onClick={() => handleVerifyOTP()}
              disabled={isLoading}
              className="w-full flex items-center justify-center px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors mb-4"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Verifying...
                </>
              ) : (
                <>
                  Verify OTP
                  <ArrowRight className="w-5 h-5 ml-2" />
                </>
              )}
            </button>
          )}

          {/* Resend OTP */}
          <div className="text-center mb-4">
            {timeLeft > 0 ? (
              <p className="text-sm text-gray-500">
                Didn't receive the code? You can resend in {formatTime(timeLeft)}
              </p>
            ) : (
              <button
                onClick={handleResendOTP}
                disabled={isResending}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium disabled:opacity-50"
              >
                {isResending ? (
                  <>
                    <RefreshCw className="w-4 h-4 inline mr-1 animate-spin" />
                    Resending...
                  </>
                ) : (
                  'Resend OTP'
                )}
              </button>
            )}
          </div>
        </>
      )}

      {/* Back Button */}
      {onBack && !isVerified && (
        <button
          onClick={onBack}
          disabled={isLoading || isResending}
          className="w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
        >
          ← Change Mobile Number
        </button>
      )}

      {/* Auto-verification notice */}
      {!isVerified && (
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            OTP will be verified automatically when all digits are entered
          </p>
        </div>
      )}
    </div>
  );
};

export default OTPVerification;
