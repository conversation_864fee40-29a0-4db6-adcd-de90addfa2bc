const express = require('express');
const router = express.Router();
const PaymentController = require('../../controllers/valet/PaymentController');
const { auth } = require('../../middleware/auth');

/**
 * Valet Payment Routes
 * Handles payment processing, gateway integration, and payment status management
 */

// Get payment options for a plaza
router.get('/options/:plazaId', auth(), PaymentController.getPaymentOptions);

// Initiate payment process
router.post('/initiate', auth(), PaymentController.initiatePayment);

// Get payment status
router.get('/status/:paymentId', auth(), PaymentController.getPaymentStatus);

// Update payment status (for gateway callbacks)
router.put('/status/:paymentId', auth(), PaymentController.updatePaymentStatus);

// Accept cash payment (for valet controllers)
router.post('/cash/accept/:paymentId', auth(), PaymentController.acceptCashPayment);

// Get pending cash payments (for valet controllers)
router.get('/cash/pending/:plazaId', auth(), PaymentController.getPendingCashPayments);

module.exports = router;
