const express = require('express');
const router = express.Router();
const PaymentController = require('../../controllers/valet/PaymentController');
const { authenticateToken } = require('../../middleware/auth');

/**
 * Valet Payment Routes
 * Handles payment processing, gateway integration, and payment status management
 */

// Get payment options for a plaza
router.get('/options/:plazaId', authenticateToken, PaymentController.getPaymentOptions);

// Initiate payment process
router.post('/initiate', authenticateToken, PaymentController.initiatePayment);

// Get payment status
router.get('/status/:paymentId', authenticateToken, PaymentController.getPaymentStatus);

// Update payment status (for gateway callbacks)
router.put('/status/:paymentId', authenticateToken, PaymentController.updatePaymentStatus);

// Accept cash payment (for valet controllers)
router.post('/cash/accept/:paymentId', authenticateToken, PaymentController.acceptCashPayment);

// Get pending cash payments (for valet controllers)
router.get('/cash/pending/:plazaId', authenticateToken, PaymentController.getPendingCashPayments);

module.exports = router;
