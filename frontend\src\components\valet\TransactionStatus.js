import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  Clock, 
  Car, 
  MapPin, 
  Phone, 
  CreditCard, 
  AlertCircle, 
  RefreshCw,
  Copy,
  Share2,
  Download
} from 'lucide-react';
import { valetCustomerApi } from '../../api/valetCustomerApi';
import { useToast } from '../../hooks/useToast';

const TransactionStatus = ({ 
  transactionId,
  pnr,
  pin,
  onRefresh,
  className = "" 
}) => {
  const [transaction, setTransaction] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  
  const toast = useToast();

  useEffect(() => {
    loadTransaction();
    
    // Auto-refresh every 30 seconds for active transactions
    const interval = setInterval(() => {
      if (transaction && ['PENDING', 'CONFIRMED', 'VEHICLE_RECEIVED'].includes(transaction.status)) {
        refreshTransaction();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [transactionId, pnr, pin]);

  const loadTransaction = async () => {
    try {
      setIsLoading(true);
      setError('');

      let response;
      if (transactionId) {
        response = await valetCustomerApi.getTransactionByPNR(transactionId);
      } else if (pnr) {
        response = await valetCustomerApi.getTransactionByPNR(pnr);
      } else if (pin) {
        response = await valetCustomerApi.getTransactionByPIN(pin);
      } else {
        throw new Error('Transaction ID, PNR, or PIN is required');
      }

      if (response.success) {
        setTransaction(response.transaction);
      } else {
        throw new Error(response.message || 'Transaction not found');
      }
      
    } catch (error) {
      console.error('Error loading transaction:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to load transaction';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshTransaction = async () => {
    try {
      setRefreshing(true);
      await loadTransaction();
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error refreshing transaction:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const copyToClipboard = (text, label) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(`${label} copied to clipboard`);
    }).catch(() => {
      toast.error('Failed to copy to clipboard');
    });
  };

  const shareTransaction = () => {
    if (navigator.share && transaction) {
      navigator.share({
        title: 'Valet Transaction Details',
        text: `PNR: ${transaction.pnr}\nPIN: ${transaction.pin}\nStatus: ${transaction.status}`,
        url: window.location.href
      }).catch(console.error);
    } else {
      copyToClipboard(
        `PNR: ${transaction.pnr}\nPIN: ${transaction.pin}\nStatus: ${transaction.status}`,
        'Transaction details'
      );
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-600 bg-green-100';
      case 'CONFIRMED':
      case 'VEHICLE_RECEIVED':
        return 'text-blue-600 bg-blue-100';
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-100';
      case 'CANCELLED':
      case 'FAILED':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="w-5 h-5" />;
      case 'CONFIRMED':
      case 'VEHICLE_RECEIVED':
        return <Car className="w-5 h-5" />;
      case 'PENDING':
        return <Clock className="w-5 h-5" />;
      case 'CANCELLED':
      case 'FAILED':
        return <AlertCircle className="w-5 h-5" />;
      default:
        return <Clock className="w-5 h-5" />;
    }
  };

  const getStatusMessage = (status) => {
    switch (status) {
      case 'PENDING':
        return 'Your valet request is being processed';
      case 'CONFIRMED':
        return 'Valet confirmed! Your vehicle is being parked';
      case 'VEHICLE_RECEIVED':
        return 'Vehicle received and parked safely';
      case 'COMPLETED':
        return 'Service completed! Vehicle returned successfully';
      case 'CANCELLED':
        return 'Transaction was cancelled';
      case 'FAILED':
        return 'Transaction failed';
      default:
        return 'Processing your request';
    }
  };

  if (isLoading) {
    return (
      <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">Loading transaction details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadTransaction}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Transaction Not Found</h3>
          <p className="text-gray-600">Please check your PNR or PIN and try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
      
      {/* Header with Status */}
      <div className="text-center mb-6">
        <div className={`inline-flex items-center px-4 py-2 rounded-full ${getStatusColor(transaction.status)} mb-4`}>
          {getStatusIcon(transaction.status)}
          <span className="ml-2 font-medium">{transaction.status.replace('_', ' ')}</span>
        </div>
        <h2 className="text-xl font-bold text-gray-900 mb-2">Valet Service</h2>
        <p className="text-gray-600">{getStatusMessage(transaction.status)}</p>
      </div>

      {/* Transaction Details */}
      <div className="space-y-4 mb-6">
        
        {/* PNR and PIN */}
        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 uppercase">PNR</p>
                <p className="font-mono font-semibold text-gray-900">{transaction.pnr}</p>
              </div>
              <button
                onClick={() => copyToClipboard(transaction.pnr, 'PNR')}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-gray-500 uppercase">PIN</p>
                <p className="font-mono font-semibold text-gray-900">{transaction.pin}</p>
              </div>
              <button
                onClick={() => copyToClipboard(transaction.pin, 'PIN')}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Customer Details */}
        <div className="p-4 border border-gray-200 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">Customer Details</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <Car className="w-4 h-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">Vehicle:</span>
              <span className="ml-2 font-medium">{transaction.vehicleNumber}</span>
            </div>
            <div className="flex items-center">
              <Phone className="w-4 h-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-600">Mobile:</span>
              <span className="ml-2 font-medium">{transaction.mobileNumber}</span>
            </div>
            {transaction.guestName && (
              <div className="flex items-center">
                <span className="text-sm text-gray-600">Guest:</span>
                <span className="ml-2 font-medium">{transaction.guestName}</span>
              </div>
            )}
          </div>
        </div>

        {/* Plaza Details */}
        <div className="p-4 border border-gray-200 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">Service Location</h3>
          <div className="space-y-2">
            <div className="flex items-center">
              <MapPin className="w-4 h-4 text-gray-400 mr-2" />
              <div>
                <p className="font-medium">{transaction.plazaName}</p>
                <p className="text-sm text-gray-600">{transaction.valetPointName}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Details */}
        {transaction.paymentAmount && (
          <div className="p-4 border border-gray-200 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-3">Payment Details</h3>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CreditCard className="w-4 h-4 text-gray-400 mr-2" />
                <span className="text-sm text-gray-600">Amount Paid:</span>
              </div>
              <span className="font-semibold text-green-600">₹{transaction.paymentAmount}</span>
            </div>
            {transaction.paymentMethod && (
              <p className="text-xs text-gray-500 mt-1">
                via {transaction.paymentMethod.toUpperCase()}
              </p>
            )}
          </div>
        )}

        {/* Timestamps */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">Timeline</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Created:</span>
              <span>{new Date(transaction.createdAt).toLocaleString()}</span>
            </div>
            {transaction.updatedAt && transaction.updatedAt !== transaction.createdAt && (
              <div className="flex justify-between">
                <span className="text-gray-600">Last Updated:</span>
                <span>{new Date(transaction.updatedAt).toLocaleString()}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3 mb-4">
        <button
          onClick={refreshTransaction}
          disabled={refreshing}
          className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
        <button
          onClick={shareTransaction}
          className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <Share2 className="w-4 h-4 mr-2" />
          Share
        </button>
      </div>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Keep your PNR and PIN safe. You'll need them to track your transaction and collect your vehicle.
        </p>
      </div>
    </div>
  );
};

export default TransactionStatus;
