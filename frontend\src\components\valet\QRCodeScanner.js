import React, { useState, useRef, useEffect } from 'react';
import { Camera, X, RefreshCw, AlertCircle, CheckCircle, QrCode } from 'lucide-react';
import QrScanner from 'qr-scanner';
import { valetCustomerApi } from '../../api/valetCustomerApi';
import { useToast } from '../../hooks/useToast';

const QRCodeScanner = ({ 
  onScanSuccess, 
  onScanError, 
  onClose,
  isOpen = false,
  className = ""
}) => {
  const [isScanning, setIsScanning] = useState(false);
  const [hasPermission, setHasPermission] = useState(null);
  const [scanResult, setScanResult] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState(null);
  const [cameraList, setCameraList] = useState([]);
  const [selectedCamera, setSelectedCamera] = useState(null);
  
  const videoRef = useRef(null);
  const scannerRef = useRef(null);
  const toast = useToast();

  // Initialize camera list
  useEffect(() => {
    const getCameras = async () => {
      try {
        const cameras = await QrScanner.listCameras(true);
        setCameraList(cameras);
        if (cameras.length > 0) {
          // Prefer back camera if available
          const backCamera = cameras.find(camera => 
            camera.label.toLowerCase().includes('back') || 
            camera.label.toLowerCase().includes('rear')
          );
          setSelectedCamera(backCamera || cameras[0]);
        }
      } catch (error) {
        console.error('Error getting cameras:', error);
        setError('Unable to access camera list');
      }
    };

    if (isOpen) {
      getCameras();
    }
  }, [isOpen]);

  // Initialize scanner when component opens
  useEffect(() => {
    if (isOpen && videoRef.current && selectedCamera && !scannerRef.current) {
      initializeScanner();
    }

    return () => {
      if (scannerRef.current) {
        scannerRef.current.destroy();
        scannerRef.current = null;
      }
    };
  }, [isOpen, selectedCamera]);

  const initializeScanner = async () => {
    try {
      setError(null);
      setIsScanning(true);

      // Create QR scanner instance
      scannerRef.current = new QrScanner(
        videoRef.current,
        handleScanResult,
        {
          onDecodeError: (error) => {
            // Ignore decode errors - they're normal when no QR code is visible
            console.debug('QR decode error (normal):', error);
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: selectedCamera?.id || 'environment'
        }
      );

      await scannerRef.current.start();
      setHasPermission(true);
      
    } catch (error) {
      console.error('Error initializing scanner:', error);
      setHasPermission(false);
      setError('Camera access denied or not available');
      setIsScanning(false);
    }
  };

  const handleScanResult = async (result) => {
    if (isValidating || scanResult) return;

    try {
      setIsValidating(true);
      setScanResult(result.data);
      
      // Stop scanning temporarily while validating
      if (scannerRef.current) {
        scannerRef.current.stop();
      }

      console.log('QR Code scanned:', result.data);
      
      // Validate QR code with backend
      const validationResult = await valetCustomerApi.validateQRCode(result.data);
      
      if (validationResult.success) {
        toast.success('QR Code validated successfully!');
        onScanSuccess({
          qrData: result.data,
          validationResult
        });
      } else {
        throw new Error(validationResult.message || 'Invalid QR code');
      }
      
    } catch (error) {
      console.error('QR validation error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to validate QR code';
      setError(errorMessage);
      toast.error(errorMessage);
      
      // Resume scanning after error
      setTimeout(() => {
        setScanResult(null);
        setError(null);
        setIsValidating(false);
        if (scannerRef.current && isOpen) {
          scannerRef.current.start();
        }
      }, 3000);
      
      if (onScanError) {
        onScanError(error);
      }
    } finally {
      setIsValidating(false);
    }
  };

  const handleClose = () => {
    if (scannerRef.current) {
      scannerRef.current.destroy();
      scannerRef.current = null;
    }
    setIsScanning(false);
    setHasPermission(null);
    setScanResult(null);
    setError(null);
    onClose();
  };

  const switchCamera = async () => {
    if (cameraList.length <= 1) return;
    
    try {
      const currentIndex = cameraList.findIndex(cam => cam.id === selectedCamera?.id);
      const nextIndex = (currentIndex + 1) % cameraList.length;
      const nextCamera = cameraList[nextIndex];
      
      if (scannerRef.current) {
        await scannerRef.current.setCamera(nextCamera.id);
        setSelectedCamera(nextCamera);
      }
    } catch (error) {
      console.error('Error switching camera:', error);
      toast.error('Failed to switch camera');
    }
  };

  const retryScanning = () => {
    setError(null);
    setScanResult(null);
    setIsValidating(false);
    if (selectedCamera) {
      initializeScanner();
    }
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center ${className}`}>
      <div className="relative w-full h-full max-w-md mx-auto">
        
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/70 to-transparent p-4">
          <div className="flex items-center justify-between text-white">
            <h2 className="text-lg font-semibold flex items-center">
              <QrCode className="w-5 h-5 mr-2" />
              Scan QR Code
            </h2>
            <button
              onClick={handleClose}
              className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Camera View */}
        <div className="relative w-full h-full">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
          />
          
          {/* Scanning Overlay */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative">
              {/* Scanning Frame */}
              <div className="w-64 h-64 border-2 border-white/50 rounded-lg relative">
                <div className="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-yellow-400 rounded-tl-lg"></div>
                <div className="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-yellow-400 rounded-tr-lg"></div>
                <div className="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-yellow-400 rounded-bl-lg"></div>
                <div className="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-yellow-400 rounded-br-lg"></div>
                
                {/* Scanning Animation */}
                {isScanning && !error && !scanResult && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-full h-1 bg-gradient-to-r from-transparent via-yellow-400 to-transparent animate-pulse"></div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Controls */}
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/70 to-transparent p-4">
          
          {/* Status Messages */}
          {hasPermission === false && (
            <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <div className="flex items-center text-red-300">
                <AlertCircle className="w-5 h-5 mr-2" />
                <span className="text-sm">Camera access required to scan QR codes</span>
              </div>
            </div>
          )}

          {error && (
            <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <div className="flex items-center text-red-300">
                <AlertCircle className="w-5 h-5 mr-2" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          {isValidating && (
            <div className="mb-4 p-3 bg-blue-500/20 border border-blue-500/30 rounded-lg">
              <div className="flex items-center text-blue-300">
                <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                <span className="text-sm">Validating QR code...</span>
              </div>
            </div>
          )}

          {scanResult && !isValidating && !error && (
            <div className="mb-4 p-3 bg-green-500/20 border border-green-500/30 rounded-lg">
              <div className="flex items-center text-green-300">
                <CheckCircle className="w-5 h-5 mr-2" />
                <span className="text-sm">QR code validated successfully!</span>
              </div>
            </div>
          )}

          {/* Control Buttons */}
          <div className="flex items-center justify-center space-x-4">
            
            {/* Camera Switch Button */}
            {cameraList.length > 1 && (
              <button
                onClick={switchCamera}
                disabled={!isScanning || isValidating}
                className="p-3 rounded-full bg-white/20 hover:bg-white/30 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <RefreshCw className="w-6 h-6 text-white" />
              </button>
            )}

            {/* Retry Button */}
            {(error || hasPermission === false) && (
              <button
                onClick={retryScanning}
                className="px-6 py-3 bg-yellow-500 hover:bg-yellow-600 text-black font-medium rounded-lg transition-colors"
              >
                Retry
              </button>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-4 text-center">
            <p className="text-white/70 text-sm">
              {isScanning && !error && !scanResult 
                ? "Position the QR code within the frame"
                : hasPermission === false
                ? "Please allow camera access to continue"
                : error
                ? "Please try again"
                : "Processing..."
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRCodeScanner;
