-- =============================================
-- CREATE MISSING VALET SYSTEM TABLES
-- Run this BEFORE executing stored procedures
-- Database: ParkwizOps
-- =============================================

USE ParkwizOps;
GO

PRINT 'Creating missing Valet system tables...';
PRINT '========================================';

-- =============================================
-- 1. CREATE PaymentGatewayTransactions Table
-- =============================================
PRINT 'Creating PaymentGatewayTransactions table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PaymentGatewayTransactions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PaymentGatewayTransactions] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [TransactionId] INT NOT NULL,
        [GatewayType] NVARCHAR(50) NOT NULL, -- RAZORPAY, PHONEPE, CASH
        [GatewayOrderId] NVARCHAR(100) NOT NULL,
        [GatewayTransactionId] NVARCHAR(100) NULL,
        [Amount] DECIMAL(10,2) NOT NULL,
        [Currency] NVARCHAR(10) NOT NULL DEFAULT 'INR',
        [PaymentMethod] NVARCHAR(50) NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SUCCESS, FAILED, CANCELLED
        [CustomerName] NVARCHAR(100) NULL,
        [CustomerEmail] NVARCHAR(100) NULL,
        [CustomerMobile] NVARCHAR(15) NULL,
        [GatewayResponse] NVARCHAR(MAX) NULL,
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [UpdatedOn] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );
    
    -- Create indexes
    CREATE INDEX IX_PaymentGatewayTransactions_TransactionId ON PaymentGatewayTransactions(TransactionId);
    CREATE INDEX IX_PaymentGatewayTransactions_GatewayOrderId ON PaymentGatewayTransactions(GatewayOrderId);
    CREATE INDEX IX_PaymentGatewayTransactions_Status ON PaymentGatewayTransactions(Status);
    CREATE INDEX IX_PaymentGatewayTransactions_CreatedOn ON PaymentGatewayTransactions(CreatedOn);
    
    PRINT 'PaymentGatewayTransactions table created successfully!';
END
ELSE
BEGIN
    PRINT 'PaymentGatewayTransactions table already exists.';
END
GO

-- =============================================
-- 2. CREATE SMSNotifications Table
-- =============================================
PRINT 'Creating SMSNotifications table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SMSNotifications]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[SMSNotifications] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [MobileNumber] NVARCHAR(15) NOT NULL,
        [Message] NVARCHAR(MAX) NOT NULL,
        [SMSType] NVARCHAR(50) NOT NULL, -- OTP_VERIFICATION, VRN_NOTIFICATION, PICKUP_READY, PAYMENT_SUCCESS
        [CustomerId] INT NULL,
        [TransactionId] INT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SENT, DELIVERED, FAILED
        [Provider] NVARCHAR(50) NOT NULL DEFAULT 'SYSTEM', -- TWILIO, MSG91, TEXTLOCAL, AWS_SNS, SIMULATION
        [ProviderId] NVARCHAR(100) NULL, -- Provider's message ID
        [DeliveryStatus] NVARCHAR(50) NULL,
        [ErrorMessage] NVARCHAR(500) NULL,
        [SentAt] DATETIME NULL,
        [DeliveredAt] DATETIME NULL,
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [IsActive] BIT NOT NULL DEFAULT 1
    );
    
    -- Create indexes
    CREATE INDEX IX_SMSNotifications_MobileNumber ON SMSNotifications(MobileNumber);
    CREATE INDEX IX_SMSNotifications_SMSType ON SMSNotifications(SMSType);
    CREATE INDEX IX_SMSNotifications_Status ON SMSNotifications(Status);
    CREATE INDEX IX_SMSNotifications_CustomerId ON SMSNotifications(CustomerId);
    CREATE INDEX IX_SMSNotifications_TransactionId ON SMSNotifications(TransactionId);
    CREATE INDEX IX_SMSNotifications_CreatedOn ON SMSNotifications(CreatedOn);
    
    PRINT 'SMSNotifications table created successfully!';
END
ELSE
BEGIN
    PRINT 'SMSNotifications table already exists.';
END
GO

-- =============================================
-- 3. CREATE CustomerSessions Table
-- =============================================
PRINT 'Creating CustomerSessions table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CustomerSessions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CustomerSessions] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [CustomerId] INT NOT NULL,
        [PlazaValetPointId] INT NOT NULL,
        [SessionData] NVARCHAR(MAX) NOT NULL, -- JSON data containing form progress
        [CurrentStep] NVARCHAR(50) NOT NULL DEFAULT 'INITIALIZED',
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'ACTIVE', -- ACTIVE, COMPLETED, CANCELLED, EXPIRED
        [ExpiresAt] DATETIME NOT NULL DEFAULT DATEADD(HOUR, 2, GETDATE()),
        [CompletedAt] DATETIME NULL,
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] INT NULL,
        [ModifiedOn] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );
    
    -- Create indexes
    CREATE INDEX IX_CustomerSessions_CustomerId ON CustomerSessions(CustomerId);
    CREATE INDEX IX_CustomerSessions_PlazaValetPointId ON CustomerSessions(PlazaValetPointId);
    CREATE INDEX IX_CustomerSessions_Status ON CustomerSessions(Status);
    CREATE INDEX IX_CustomerSessions_CurrentStep ON CustomerSessions(CurrentStep);
    CREATE INDEX IX_CustomerSessions_CreatedOn ON CustomerSessions(CreatedOn);
    CREATE INDEX IX_CustomerSessions_ExpiresAt ON CustomerSessions(ExpiresAt);
    
    PRINT 'CustomerSessions table created successfully!';
END
ELSE
BEGIN
    PRINT 'CustomerSessions table already exists.';
END
GO

-- =============================================
-- 4. CREATE ValetQRCodes Table
-- =============================================
PRINT 'Creating ValetQRCodes table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ValetQRCodes]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ValetQRCodes] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [PlazaId] INT NOT NULL,
        [QRData] NVARCHAR(200) NOT NULL UNIQUE,
        [QRType] NVARCHAR(50) NOT NULL DEFAULT 'VALET_POINT',
        [ExpiresAt] DATETIME NULL,
        [UsageCount] INT NOT NULL DEFAULT 0,
        [MaxUsage] INT NULL, -- NULL means unlimited
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] INT NULL,
        [ModifiedOn] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );
    
    -- Create indexes
    CREATE INDEX IX_ValetQRCodes_PlazaId ON ValetQRCodes(PlazaId);
    CREATE INDEX IX_ValetQRCodes_QRData ON ValetQRCodes(QRData);
    CREATE INDEX IX_ValetQRCodes_QRType ON ValetQRCodes(QRType);
    CREATE INDEX IX_ValetQRCodes_IsActive ON ValetQRCodes(IsActive);
    CREATE INDEX IX_ValetQRCodes_CreatedOn ON ValetQRCodes(CreatedOn);
    
    PRINT 'ValetQRCodes table created successfully!';
END
ELSE
BEGIN
    PRINT 'ValetQRCodes table already exists.';
END
GO

-- =============================================
-- 5. CREATE PlazaRazorPayConfiguration Table
-- =============================================
PRINT 'Creating PlazaRazorPayConfiguration table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PlazaRazorPayConfiguration]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PlazaRazorPayConfiguration] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [PlazaId] INT NOT NULL,
        [KeyId] NVARCHAR(100) NOT NULL,
        [KeySecret] NVARCHAR(100) NOT NULL,
        [WebhookSecret] NVARCHAR(100) NULL,
        [IsTestMode] BIT NOT NULL DEFAULT 1,
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] INT NULL,
        [ModifiedOn] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );
    
    -- Create indexes
    CREATE INDEX IX_PlazaRazorPayConfiguration_PlazaId ON PlazaRazorPayConfiguration(PlazaId);
    CREATE INDEX IX_PlazaRazorPayConfiguration_IsActive ON PlazaRazorPayConfiguration(IsActive);
    
    PRINT 'PlazaRazorPayConfiguration table created successfully!';
END
ELSE
BEGIN
    PRINT 'PlazaRazorPayConfiguration table already exists.';
END
GO

-- =============================================
-- 6. CREATE PlazaPhonePeConfiguration Table
-- =============================================
PRINT 'Creating PlazaPhonePeConfiguration table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PlazaPhonePeConfiguration]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PlazaPhonePeConfiguration] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [PlazaId] INT NOT NULL,
        [MerchantId] NVARCHAR(100) NOT NULL,
        [SaltKey] NVARCHAR(100) NOT NULL,
        [SaltIndex] INT NOT NULL DEFAULT 1,
        [IsTestMode] BIT NOT NULL DEFAULT 1,
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] INT NULL,
        [ModifiedOn] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );
    
    -- Create indexes
    CREATE INDEX IX_PlazaPhonePeConfiguration_PlazaId ON PlazaPhonePeConfiguration(PlazaId);
    CREATE INDEX IX_PlazaPhonePeConfiguration_IsActive ON PlazaPhonePeConfiguration(IsActive);
    
    PRINT 'PlazaPhonePeConfiguration table created successfully!';
END
ELSE
BEGIN
    PRINT 'PlazaPhonePeConfiguration table already exists.';
END
GO

-- =============================================
-- VERIFICATION - Check all tables exist
-- =============================================
PRINT '';
PRINT 'Verifying all Valet tables...';
PRINT '==============================';

SELECT 
    TABLE_NAME as TableName,
    TABLE_TYPE as TableType
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME IN (
    'PaymentGatewayTransactions',
    'SMSNotifications', 
    'CustomerSessions',
    'ValetQRCodes',
    'PlazaRazorPayConfiguration',
    'PlazaPhonePeConfiguration'
)
ORDER BY TABLE_NAME;

PRINT '';
PRINT 'All Valet tables created successfully!';
PRINT 'Now run EXECUTE_ALL_VALET_STORED_PROCEDURES.sql';
PRINT '===============================================';
