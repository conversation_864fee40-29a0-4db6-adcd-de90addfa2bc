const express = require('express');
const router = express.Router();
const transactionController = require('../../controllers/valet/ValetTransactionController');
const { authenticateToken, authorizeRoles } = require('../../middleware/auth');

/**
 * Valet Transaction Routes
 * Handles valet parking transactions and status management
 */

// Public routes (for customer app/QR code scanning)
router.post('/create', transactionController.createValetTransaction);
router.get('/search/:identifier', transactionController.getTransactionByPNROrPin);

// Controller/Admin routes (require authentication)
router.put('/:transactionId/status', 
  authenticateToken, 
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController', 'ValetDriver']),
  transactionController.updateTransactionStatus
);

router.get('/valet-point/:plazaValetPointId/active', 
  authenticateToken, 
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  transactionController.getActiveTransactionsByValetPoint
);

module.exports = router;
