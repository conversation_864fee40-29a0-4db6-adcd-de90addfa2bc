-- =============================================
-- EXECUTE ALL VALET STORED PROCEDURES
-- Run this script in Azure Data Studio or SSMS
-- Database: ParkwizOps
-- =============================================

USE ParkwizOps;
GO

PRINT 'Starting Valet Stored Procedures Execution...';
PRINT '================================================';

-- =============================================
-- 1. CREATE sp_Valet_Payment_GetOptions
-- =============================================
PRINT 'Creating sp_Valet_Payment_GetOptions...';

CREATE OR ALTER PROCEDURE sp_Valet_Payment_GetOptions
    @plazaId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validate plaza exists and is active
        IF NOT EXISTS (SELECT 1 FROM Plaza WHERE Id = @plazaId AND IsActive = 1)
        BEGIN
            RAISERROR('Plaza not found or inactive', 16, 1);
            RETURN;
        END
        
        -- Get payment configuration for the plaza
        SELECT 
            p.Id as PlazaId,
            p.PlazaName,
            p.ValetCharges,
            CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as RazorPayEnabled,
            CASE WHEN pp.Id IS NOT NULL AND pp.IsActive = 1 THEN 1 ELSE 0 END as PhonePeEnabled,
            1 as CashEnabled, -- Cash is always enabled
            CASE WHEN (rp.Id IS NOT NULL AND rp.IsActive = 1) OR (pp.Id IS NOT NULL AND pp.IsActive = 1) THEN 1 ELSE 0 END as UPIEnabled,
            CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as CardEnabled,
            CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as NetBankingEnabled,
            rp.KeyId as RazorPayKeyId,
            pp.MerchantId as PhonePeMerchantId
        FROM Plaza p
        LEFT JOIN PlazaRazorPayConfiguration rp ON p.Id = rp.PlazaId AND rp.IsActive = 1
        LEFT JOIN PlazaPhonePeConfiguration pp ON p.Id = pp.PlazaId AND pp.IsActive = 1
        WHERE p.Id = @plazaId AND p.IsActive = 1;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'sp_Valet_Payment_GetOptions created successfully!';

-- =============================================
-- 2. CREATE sp_Valet_Payment_Initiate
-- =============================================
PRINT 'Creating sp_Valet_Payment_Initiate...';

CREATE OR ALTER PROCEDURE sp_Valet_Payment_Initiate
    @TransactionId INT,
    @PaymentMethod NVARCHAR(50),
    @Amount DECIMAL(10,2),
    @CustomerId INT,
    @CustomerName NVARCHAR(100) = NULL,
    @CustomerEmail NVARCHAR(100) = NULL,
    @CustomerMobile NVARCHAR(15) = NULL,
    @CreatedBy INT,
    @PaymentId INT OUTPUT,
    @OrderId NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate transaction exists
        IF NOT EXISTS (SELECT 1 FROM ParkingTransactions WHERE Id = @TransactionId)
        BEGIN
            RAISERROR('Transaction not found', 16, 1);
            RETURN;
        END
        
        -- Validate customer exists
        IF NOT EXISTS (SELECT 1 FROM Customer WHERE Id = @CustomerId AND IsActive = 1)
        BEGIN
            RAISERROR('Customer not found or inactive', 16, 1);
            RETURN;
        END
        
        -- Validate amount
        IF @Amount <= 0
        BEGIN
            RAISERROR('Amount must be greater than 0', 16, 1);
            RETURN;
        END
        
        -- Generate unique order ID
        SET @OrderId = 'VALET_' + CONVERT(NVARCHAR(20), GETDATE(), 112) + '_' + CAST(@TransactionId AS NVARCHAR(10)) + '_' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS NVARCHAR(4));
        
        -- Insert payment record
        INSERT INTO PaymentGatewayTransactions (
            TransactionId,
            GatewayType,
            GatewayOrderId,
            Amount,
            Currency,
            PaymentMethod,
            Status,
            CustomerName,
            CustomerEmail,
            CustomerMobile,
            CreatedBy,
            CreatedOn
        )
        VALUES (
            @TransactionId,
            @PaymentMethod,
            @OrderId,
            @Amount,
            'INR',
            @PaymentMethod,
            CASE WHEN @PaymentMethod = 'CASH' THEN 'PENDING_CASH' ELSE 'PENDING' END,
            @CustomerName,
            @CustomerEmail,
            @CustomerMobile,
            @CreatedBy,
            GETDATE()
        );
        
        SET @PaymentId = SCOPE_IDENTITY();
        
        -- Update transaction status
        UPDATE ParkingTransactions
        SET Status = 'PAYMENT_INITIATED',
            ModifiedBy = @CreatedBy,
            ModifiedOn = GETDATE()
        WHERE Id = @TransactionId;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'sp_Valet_Payment_Initiate created successfully!';

-- =============================================
-- 3. CREATE sp_Valet_Payment_UpdateStatus
-- =============================================
PRINT 'Creating sp_Valet_Payment_UpdateStatus...';

CREATE OR ALTER PROCEDURE sp_Valet_Payment_UpdateStatus
    @PaymentId INT,
    @Status NVARCHAR(50),
    @GatewayTransactionId NVARCHAR(100) = NULL,
    @GatewayResponse NVARCHAR(MAX) = NULL,
    @UpdatedBy INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate payment exists
        IF NOT EXISTS (SELECT 1 FROM PaymentGatewayTransactions WHERE Id = @PaymentId)
        BEGIN
            RAISERROR('Payment not found', 16, 1);
            RETURN;
        END
        
        -- Get transaction ID for status update
        DECLARE @TransactionId INT;
        SELECT @TransactionId = TransactionId 
        FROM PaymentGatewayTransactions 
        WHERE Id = @PaymentId;
        
        -- Update payment status
        UPDATE PaymentGatewayTransactions
        SET Status = @Status,
            GatewayTransactionId = @GatewayTransactionId,
            GatewayResponse = @GatewayResponse,
            UpdatedOn = GETDATE()
        WHERE Id = @PaymentId;
        
        -- Update main transaction status based on payment status
        IF @Status = 'SUCCESS'
        BEGIN
            UPDATE ParkingTransactions
            SET Status = 'PAYMENT_COMPLETED',
                ModifiedBy = ISNULL(@UpdatedBy, 1),
                ModifiedOn = GETDATE()
            WHERE Id = @TransactionId;
        END
        ELSE IF @Status = 'FAILED'
        BEGIN
            UPDATE ParkingTransactions
            SET Status = 'PAYMENT_FAILED',
                ModifiedBy = ISNULL(@UpdatedBy, 1),
                ModifiedOn = GETDATE()
            WHERE Id = @TransactionId;
        END
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'sp_Valet_Payment_UpdateStatus created successfully!';

-- =============================================
-- 4. CREATE sp_Valet_SMS_Send
-- =============================================
PRINT 'Creating sp_Valet_SMS_Send...';

CREATE OR ALTER PROCEDURE sp_Valet_SMS_Send
    @MobileNumber NVARCHAR(15),
    @Message NVARCHAR(MAX),
    @SMSType NVARCHAR(50),
    @CustomerId INT = NULL,
    @TransactionId INT = NULL,
    @CreatedBy INT = NULL,
    @SMSId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validate mobile number
        IF @MobileNumber IS NULL OR LEN(@MobileNumber) < 10
        BEGIN
            RAISERROR('Invalid mobile number', 16, 1);
            RETURN;
        END
        
        -- Validate message
        IF @Message IS NULL OR LEN(@Message) = 0
        BEGIN
            RAISERROR('Message cannot be empty', 16, 1);
            RETURN;
        END
        
        -- Insert SMS record
        INSERT INTO SMSNotifications (
            MobileNumber,
            Message,
            SMSType,
            CustomerId,
            TransactionId,
            Status,
            Provider,
            CreatedBy,
            CreatedOn
        )
        VALUES (
            @MobileNumber,
            @Message,
            ISNULL(@SMSType, 'GENERAL'),
            @CustomerId,
            @TransactionId,
            'PENDING',
            'SYSTEM',
            ISNULL(@CreatedBy, 1),
            GETDATE()
        );
        
        SET @SMSId = SCOPE_IDENTITY();
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
GO

PRINT 'sp_Valet_SMS_Send created successfully!';

-- =============================================
-- VERIFICATION - Test all stored procedures
-- =============================================
PRINT '';
PRINT 'Verifying stored procedures...';
PRINT '================================';

-- Check if all procedures exist
SELECT 
    name as ProcedureName,
    create_date as CreatedDate,
    modify_date as ModifiedDate
FROM sys.procedures 
WHERE name LIKE 'sp_Valet_%'
ORDER BY name;

PRINT '';
PRINT 'All Valet Stored Procedures created successfully!';
PRINT 'You can now test the API endpoints.';
PRINT '================================================';
