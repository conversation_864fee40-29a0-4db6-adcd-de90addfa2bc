const sql = require('mssql');
require('dotenv').config({path: './backend/.env'});

async function checkTables() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: 'ParkwizOps',
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    console.log('🔍 Checking existing table structures...');
    
    // Check if ParkingTransactions table exists
    const tableExistsQuery = `
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'ParkingTransactions'
    `;
    
    const existsResult = await sql.query(tableExistsQuery);
    console.log(`\n🔍 ParkingTransactions table exists: ${existsResult.recordset[0].count > 0}`);

    if (existsResult.recordset[0].count > 0) {
      // Check ParkingTransactions table structure
      const parkingTransQuery = `
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'ParkingTransactions'
        ORDER BY ORDINAL_POSITION
      `;
      
      const parkingResult = await sql.query(parkingTransQuery);
      console.log('\n📋 ParkingTransactions table columns:');
      parkingResult.recordset.forEach(col => {
        console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
      });
    }

    // Check Modules table structure
    const modulesQuery = `
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Modules'
      ORDER BY ORDINAL_POSITION
    `;
    
    const modulesResult = await sql.query(modulesQuery);
    console.log('\n📋 Modules table columns:');
    modulesResult.recordset.forEach(col => {
      console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
    });

    // Check SubModules table structure
    const subModulesQuery = `
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'SubModules'
      ORDER BY ORDINAL_POSITION
    `;

    const subModulesResult = await sql.query(subModulesQuery);
    console.log('\n📋 SubModules table columns:');
    subModulesResult.recordset.forEach(col => {
      console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
    });

    // Check Roles table structure
    const rolesQuery = `
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'Roles'
      ORDER BY ORDINAL_POSITION
    `;

    const rolesResult = await sql.query(rolesQuery);
    console.log('\n📋 Roles table columns:');
    rolesResult.recordset.forEach(col => {
      console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
    });

    // Check what valet tables were created
    const valetTablesQuery = `
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME IN (
        'Customer', 'CustomerVehicle', 'OTP', 'ValetDrivers',
        'ValetControllers', 'VehicleStatusTracking', 'ValetQRCodes',
        'SMSNotifications', 'PlazaRazorPayConfiguration', 'PlazaPhonePeConfiguration'
      )
      ORDER BY TABLE_NAME
    `;

    const valetResult = await sql.query(valetTablesQuery);
    console.log('\n📋 Valet tables created:');
    valetResult.recordset.forEach(row => {
      console.log(`   ✅ ${row.TABLE_NAME}`);
    });

    await sql.close();
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkTables();
