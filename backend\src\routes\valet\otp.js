const express = require('express');
const router = express.Router();
const otpController = require('../../controllers/valet/OTPController');
const { authenticateToken, authorizeRoles } = require('../../middleware/auth');

/**
 * Valet OTP Routes
 * Handles OTP generation, verification, and management
 */

// Public routes (for customer app/QR code scanning)
router.post('/generate', otpController.generateOTP);
router.post('/verify', otpController.verifyOTP);
router.post('/resend', otpController.resendOTP);
router.get('/status/:mobileNumber', otpController.getOTPStatus);

// Admin routes (require authentication)
router.post('/cleanup', 
  authenticateToken, 
  authorizeRoles(['SuperAdmin', 'CompanyAdmin']),
  otpController.cleanupExpiredOTPs
);

router.get('/statistics', 
  authenticateToken, 
  authorizeRoles(['SuperAdmin', 'CompanyAdmin']),
  otpController.getOTPStatistics
);

module.exports = router;
