const db = require('../../config/database');
const sql = require('mssql');
const { v4: uuidv4 } = require('uuid');

/**
 * Valet Transaction Controller
 * Handles valet parking transactions, vehicle registration, and status management
 */

// Create new valet transaction
exports.createValetTransaction = async (req, res) => {
  try {
    const {
      customerId,
      customerVehicleNumber,
      customerMobileNumber,
      customerName,
      plazaValetPointId,
      isAnyValuableItem = false,
      anyValuableItem = null,
      valetFee = 0,
      parkingFee = 0,
      paymentType = 1, // 1: Cash, 2: Online
      payAt = 1 // 1: Entry, 2: Exit
    } = req.body;

    // Validate required fields
    if (!customerVehicleNumber || !customerMobileNumber || !plazaValetPointId) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle number, mobile number, and valet point are required'
      });
    }

    // Get plaza valet point details
    const valetPointQuery = `
      SELECT pvp.*, p.<PERSON>, p.CompanyId, c.CompanyName
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE pvp.Id = @plazaValetPointId AND pvp.IsActive = 1
    `;
    
    const valetPointResult = await db.query(valetPointQuery, { plazaValetPointId });
    
    if (valetPointResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Valet point not found or inactive'
      });
    }

    const valetPoint = valetPointResult.recordset[0];

    // Check if vehicle is already parked (active transaction exists)
    const activeTransactionQuery = `
      SELECT Id FROM ParkingTransactions 
      WHERE CustomerVehicleNumber = @customerVehicleNumber 
        AND TransactionStatus IN (1, 2, 3) 
        AND ExitDateTime IS NULL
    `;
    
    const activeTransaction = await db.query(activeTransactionQuery, { customerVehicleNumber });
    
    if (activeTransaction.recordset.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle is already parked. Please complete the existing transaction first.'
      });
    }

    // Generate PNR and Parking Pin
    const pnrNumber = uuidv4();
    const parkingPin = Math.floor(100000 + Math.random() * 900000);

    // Calculate total fee
    const totalFee = parseFloat(valetFee) + parseFloat(parkingFee);

    let transactionId = null;

    try {
      // Use stored procedure to create transaction
      const transactionResult = await db.query(`
        DECLARE @NewId DECIMAL(18,0);
        EXEC sp_Valet_Transaction_Create
          @PNRNumber = @pnrNumber,
          @ParkingPin = @parkingPin,
          @CompanyId = @companyId,
          @PlazaId = @plazaId,
          @CustomerVehicleNumber = @customerVehicleNumber,
          @CustomerMobileNumber = @customerMobileNumber,
          @CustomerName = @customerName,
          @IsAnyValuableItem = @isAnyValuableItem,
          @AnyValuableItem = @anyValuableItem,
          @ValetFee = @valetFee,
          @ParkingFee = @parkingFee,
          @TotalFee = @totalFee,
          @PayAt = @payAt,
          @PaymentType = @paymentType,
          @EntryBy = @entryBy,
          @PlazaValetPointId = @plazaValetPointId,
          @NewId = @NewId OUTPUT;
        SELECT @NewId as Id;
      `, {
        pnrNumber,
        parkingPin,
        companyId: valetPoint.CompanyId,
        plazaId: valetPoint.PlazaId,
        customerVehicleNumber: customerVehicleNumber.toUpperCase(),
        customerMobileNumber,
        customerName: customerName || null,
        isAnyValuableItem,
        anyValuableItem,
        valetFee,
        parkingFee,
        totalFee,
        payAt,
        paymentType,
        entryBy: req.user?.id || 1,
        plazaValetPointId
      });

      transactionId = transactionResult.recordset[0].Id;

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);

      const insertQuery = `
        INSERT INTO ParkingTransactions (
          PNRNumber, ParkingPin, CompanyId, PlazaId, CustomerVehicleNumber,
          CustomerMobileNumber, CustomerName, IsAnyValuableItem, AnyValuableItem,
          ValetFee, ParkingFee, TotalFee, Source, PayAt, PaymentType,
          IsPaymentCompleted, VehicleType, EntryBy, EntryDateTime,
          PlazaValetPointId, TransactionStatus
        )
        OUTPUT INSERTED.Id
        VALUES (
          @pnrNumber, @parkingPin, @companyId, @plazaId, @customerVehicleNumber,
          @customerMobileNumber, @customerName, @isAnyValuableItem, @anyValuableItem,
          @valetFee, @parkingFee, @totalFee, 2, @payAt, @paymentType,
          0, 1, @entryBy, GETDATE(),
          @plazaValetPointId, 1
        )
      `;

      const transactionResult = await db.query(insertQuery, {
        pnrNumber,
        parkingPin,
        companyId: valetPoint.CompanyId,
        plazaId: valetPoint.PlazaId,
        customerVehicleNumber: customerVehicleNumber.toUpperCase(),
        customerMobileNumber,
        customerName: customerName || null,
        isAnyValuableItem,
        anyValuableItem,
        valetFee,
        parkingFee,
        totalFee,
        payAt,
        paymentType,
        entryBy: req.user?.id || 1,
        plazaValetPointId
      });

      transactionId = transactionResult.recordset[0].Id;
    }

    // Add vehicle status tracking
    const statusQuery = `
      INSERT INTO VehicleStatusTracking (TransactionId, Status, UpdatedBy, UpdatedOn, Remarks)
      VALUES (@transactionId, 'REGISTERED', @updatedBy, GETDATE(), 'Vehicle registered for valet parking')
    `;
    
    await db.query(statusQuery, {
      transactionId,
      updatedBy: req.user?.id || 1
    });

    // Send VRN SMS notification
    const smsQuery = `
      INSERT INTO SMSNotifications (MobileNumber, Message, SMSType, Status, TransactionId, CreatedOn)
      VALUES (@mobileNumber, @message, 'VRN', 'PENDING', @transactionId, GETDATE())
    `;
    
    const smsMessage = `Vehicle ${customerVehicleNumber} registered for valet parking at ${valetPoint.PlazaName}. PNR: ${pnrNumber.substring(0, 8)}. Pin: ${parkingPin}`;
    
    await db.query(smsQuery, {
      mobileNumber: customerMobileNumber,
      message: smsMessage,
      transactionId
    });

    res.json({
      success: true,
      message: 'Valet transaction created successfully',
      data: {
        transactionId,
        pnrNumber,
        parkingPin,
        totalFee,
        valetPoint: {
          name: valetPoint.ValetPointName,
          plazaName: valetPoint.PlazaName,
          companyName: valetPoint.CompanyName
        },
        status: 'REGISTERED'
      }
    });

  } catch (error) {
    console.error('Error in createValetTransaction:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create valet transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get transaction by PNR or Pin
exports.getTransactionByPNROrPin = async (req, res) => {
  try {
    const { identifier } = req.params; // Can be PNR or Pin
    const { type = 'pnr' } = req.query; // 'pnr' or 'pin'

    let result = null;

    try {
      // Use stored procedure to get transaction
      if (type === 'pin') {
        result = await db.query(`
          EXEC sp_Valet_Transaction_GetByPin @ParkingPin = @identifier
        `, { identifier: parseInt(identifier) });
      } else {
        result = await db.query(`
          EXEC sp_Valet_Transaction_GetByPNR @PNRNumber = @identifier
        `, { identifier });
      }

      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Transaction not found'
        });
      }

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);

      let query = `
        SELECT
          pt.*,
          pvp.ValetPointName,
          p.PlazaName,
          c.CompanyName,
          vst.Status as CurrentStatus,
          vst.UpdatedOn as StatusUpdatedOn
        FROM ParkingTransactions pt
        JOIN PlazaValetPoint pvp ON pt.PlazaValetPointId = pvp.Id
        JOIN Plaza p ON pt.PlazaId = p.Id
        JOIN tblCompanyMaster c ON pt.CompanyId = c.Id
        LEFT JOIN (
          SELECT DISTINCT TransactionId, Status, UpdatedOn,
                 ROW_NUMBER() OVER (PARTITION BY TransactionId ORDER BY UpdatedOn DESC) as rn
          FROM VehicleStatusTracking
        ) vst ON pt.Id = vst.TransactionId AND vst.rn = 1
        WHERE
      `;

      let queryParams = {};

      if (type === 'pin') {
        query += `pt.ParkingPin = @identifier`;
        queryParams.identifier = parseInt(identifier);
      } else {
        query += `pt.PNRNumber LIKE @identifier`;
        queryParams.identifier = `%${identifier}%`;
      }

      result = await db.query(query, queryParams);

      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Transaction not found'
        });
      }
    }

    const transaction = result.recordset[0];

    // Get vehicle images
    const imagesQuery = `
      SELECT Id, CreatedOn
      FROM ParkingTransactionVehicleImages
      WHERE TransactionId = @transactionId AND IsActive = 1
      ORDER BY CreatedOn
    `;
    
    const imagesResult = await db.query(imagesQuery, { transactionId: transaction.Id });

    res.json({
      success: true,
      transaction: {
        ...transaction,
        vehicleImages: imagesResult.recordset
      },
      message: 'Transaction retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getTransactionByPNROrPin:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve transaction',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update transaction status
exports.updateTransactionStatus = async (req, res) => {
  try {
    const { transactionId } = req.params;
    const { status, remarks, driverId, parkingBayId } = req.body;

    // Validate status
    const validStatuses = [
      'REGISTERED', 'PAYMENT_COMPLETED', 'DRIVER_ASSIGNED', 
      'PARKED', 'PICKUP_REQUESTED', 'IN_TRANSIT', 'DELIVERED'
    ];
    
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status'
      });
    }

    try {
      // Use stored procedure to update transaction status
      const updateResult = await db.query(`
        EXEC sp_Valet_Transaction_UpdateStatus
          @TransactionId = @transactionId,
          @Status = @status,
          @DriverId = @driverId,
          @ParkingBayId = @parkingBayId,
          @Remarks = @remarks,
          @UpdatedBy = @updatedBy
      `, {
        transactionId,
        status,
        driverId: driverId || null,
        parkingBayId: parkingBayId || null,
        remarks: remarks || null,
        updatedBy: req.user?.id || 1
      });

      // Check if update was successful
      if (updateResult.recordset.length === 0 || updateResult.recordset[0].Success !== 1) {
        return res.status(400).json({
          success: false,
          message: updateResult.recordset[0]?.Message || 'Failed to update transaction status'
        });
      }

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);

      // Check if transaction exists
      const transactionQuery = `
        SELECT Id, TransactionStatus FROM ParkingTransactions
        WHERE Id = @transactionId
      `;

      const transactionResult = await db.query(transactionQuery, { transactionId });

      if (transactionResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Transaction not found'
        });
      }

      // Update transaction based on status
      let updateQuery = `UPDATE ParkingTransactions SET `;
      let updateParams = { transactionId };

      switch (status) {
        case 'DRIVER_ASSIGNED':
          if (!driverId) {
            return res.status(400).json({
              success: false,
              message: 'Driver ID is required for driver assignment'
            });
          }
          updateQuery += `DriverId = @driverId, DriverAssignedBy = @assignedBy, DriverAssignedOn = GETDATE()`;
          updateParams.driverId = driverId;
          updateParams.assignedBy = req.user?.id || 1;
          break;

        case 'PARKED':
          if (parkingBayId) {
            updateQuery += `ParkingBayId = @parkingBayId, ParkedBayAssignedBy = @assignedBy, ParkedBayAssignedOn = GETDATE()`;
            updateParams.parkingBayId = parkingBayId;
            updateParams.assignedBy = req.user?.id || 1;
          }
          break;

        case 'PICKUP_REQUESTED':
          updateQuery += `RequestMyVehicleDateTime = GETDATE(), RequestMyVehicleBy = @requestedBy`;
          updateParams.requestedBy = req.user?.id || 1;
          break;

        case 'DELIVERED':
          updateQuery += `ExitDateTime = GETDATE(), ExitBy = @exitBy, TransactionStatus = 4`;
          updateParams.exitBy = req.user?.id || 1;
          break;
      }

      updateQuery += ` WHERE Id = @transactionId`;

      await db.query(updateQuery, updateParams);

      // Add status tracking
      const statusQuery = `
        INSERT INTO VehicleStatusTracking (TransactionId, Status, UpdatedBy, UpdatedOn, Remarks)
        VALUES (@transactionId, @status, @updatedBy, GETDATE(), @remarks)
      `;

      await db.query(statusQuery, {
        transactionId,
        status,
        updatedBy: req.user?.id || 1,
        remarks: remarks || null
      });
    }

    res.json({
      success: true,
      message: `Transaction status updated to ${status}`,
      data: {
        transactionId,
        status,
        updatedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error in updateTransactionStatus:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update transaction status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get active transactions for a valet point
exports.getActiveTransactionsByValetPoint = async (req, res) => {
  try {
    const { plazaValetPointId } = req.params;
    const { status } = req.query;

    let query = `
      SELECT 
        pt.Id,
        pt.PNRNumber,
        pt.ParkingPin,
        pt.CustomerVehicleNumber,
        pt.CustomerMobileNumber,
        pt.CustomerName,
        pt.EntryDateTime,
        pt.TotalFee,
        pt.IsPaymentCompleted,
        vst.Status as CurrentStatus,
        vst.UpdatedOn as StatusUpdatedOn,
        vd.PhoneNumber as DriverPhone,
        u.Name as DriverName,
        pb.ParkingBayName
      FROM ParkingTransactions pt
      LEFT JOIN (
        SELECT DISTINCT TransactionId, Status, UpdatedOn,
               ROW_NUMBER() OVER (PARTITION BY TransactionId ORDER BY UpdatedOn DESC) as rn
        FROM VehicleStatusTracking
      ) vst ON pt.Id = vst.TransactionId AND vst.rn = 1
      LEFT JOIN ValetDrivers vd ON pt.DriverId = vd.Id
      LEFT JOIN Users u ON vd.UserId = u.Id
      LEFT JOIN ParkingBay pb ON pt.ParkingBayId = pb.Id
      WHERE pt.PlazaValetPointId = @plazaValetPointId 
        AND pt.TransactionStatus IN (1, 2, 3)
        AND pt.ExitDateTime IS NULL
    `;

    const queryParams = { plazaValetPointId };

    if (status) {
      query += ` AND vst.Status = @status`;
      queryParams.status = status;
    }

    query += ` ORDER BY pt.EntryDateTime DESC`;

    const result = await db.query(query, queryParams);

    res.json({
      success: true,
      transactions: result.recordset,
      message: 'Active transactions retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getActiveTransactionsByValetPoint:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve active transactions',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
