const db = require('../../config/database');
const sql = require('mssql');
const { v4: uuidv4 } = require('uuid');

/**
 * QR Code Controller
 * Handles QR code generation, scanning, and management for valet services
 */

// Generate QR code for valet point
exports.generateQRCode = async (req, res) => {
  try {
    const { plazaValetPointId, qrType = 'VALET_POINT' } = req.body;

    // Validate required fields
    if (!plazaValetPointId) {
      return res.status(400).json({
        success: false,
        message: 'Plaza valet point ID is required'
      });
    }

    // Verify valet point exists
    const valetPointQuery = `
      SELECT pvp.*, p.PlazaName, c.CompanyName
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE pvp.Id = @plazaValetPointId AND pvp.IsActive = 1
    `;
    
    const valetPointResult = await db.query(valetPointQuery, { plazaValetPointId });
    
    if (valetPointResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Valet point not found or inactive'
      });
    }

    const valetPoint = valetPointResult.recordset[0];

    // Generate unique QR data
    const qrData = `VALET_${plazaValetPointId}_${Date.now()}_${uuidv4().substring(0, 8)}`;

    let result = null;
    
    try {
      // Use stored procedure to generate QR code
      result = await db.query(`
        DECLARE @NewId INT;
        EXEC sp_Valet_QRCode_Generate 
          @PlazaId = @plazaId,
          @QRData = @qrData,
          @QRType = @qrType,
          @CreatedBy = @createdBy,
          @NewId = @NewId OUTPUT;
        SELECT @NewId as Id;
      `, {
        plazaId: valetPoint.PlazaId,
        qrData,
        qrType,
        createdBy: req.user?.id || 1
      });

      if (!result.recordset[0] || !result.recordset[0].Id) {
        throw new Error('Failed to generate QR code using stored procedure');
      }

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const insertQuery = `
        INSERT INTO ValetQRCodes (PlazaId, QRData, QRType, IsActive, CreatedBy, CreatedOn)
        OUTPUT INSERTED.Id
        VALUES (@plazaId, @qrData, @qrType, 1, @createdBy, GETDATE())
      `;
      
      result = await db.query(insertQuery, {
        plazaId: valetPoint.PlazaId,
        qrData,
        qrType,
        createdBy: req.user?.id || 1
      });
    }

    res.status(201).json({
      success: true,
      message: 'QR code generated successfully',
      data: {
        id: result.recordset[0].Id,
        qrData,
        qrType,
        plazaValetPointId,
        plazaName: valetPoint.PlazaName,
        companyName: valetPoint.CompanyName,
        createdAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error in generateQRCode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate QR code',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get QR code by data (for scanning)
exports.getQRCodeByData = async (req, res) => {
  try {
    const { qrData } = req.params;

    if (!qrData) {
      return res.status(400).json({
        success: false,
        message: 'QR data is required'
      });
    }

    let result = null;
    
    try {
      // Use stored procedure to get QR code
      result = await db.query(`
        EXEC sp_Valet_QRCode_GetByData @QRData = @qrData
      `, { qrData });

      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'QR code not found or inactive'
        });
      }

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const qrQuery = `
        SELECT 
          vqr.*,
          p.PlazaName,
          c.CompanyName,
          pvp.ValetPointName
        FROM ValetQRCodes vqr
        JOIN Plaza p ON vqr.PlazaId = p.Id
        JOIN tblCompanyMaster c ON p.CompanyId = c.Id
        LEFT JOIN PlazaValetPoint pvp ON p.Id = pvp.PlazaId
        WHERE vqr.QRData = @qrData AND vqr.IsActive = 1
      `;
      
      result = await db.query(qrQuery, { qrData });
      
      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'QR code not found or inactive'
        });
      }
    }

    const qrCode = result.recordset[0];

    res.json({
      success: true,
      message: 'QR code retrieved successfully',
      data: qrCode
    });

  } catch (error) {
    console.error('Error in getQRCodeByData:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve QR code',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all QR codes for a plaza
exports.getQRCodesByPlaza = async (req, res) => {
  try {
    const { plazaId } = req.params;
    const { 
      pageNumber = 1, 
      pageSize = 50,
      qrType,
      isActive = true 
    } = req.query;

    if (!plazaId) {
      return res.status(400).json({
        success: false,
        message: 'Plaza ID is required'
      });
    }

    let result = null;
    
    try {
      // Use stored procedure to get QR codes by plaza
      result = await db.query(`
        EXEC sp_Valet_QRCode_GetByPlaza 
          @PlazaId = @plazaId,
          @PageNumber = @pageNumber,
          @PageSize = @pageSize
      `, {
        plazaId: parseInt(plazaId),
        pageNumber: parseInt(pageNumber),
        pageSize: parseInt(pageSize)
      });

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      let query = `
        SELECT 
          vqr.*,
          p.PlazaName,
          c.CompanyName
        FROM ValetQRCodes vqr
        JOIN Plaza p ON vqr.PlazaId = p.Id
        JOIN tblCompanyMaster c ON p.CompanyId = c.Id
        WHERE vqr.PlazaId = @plazaId
      `;
      
      const queryParams = { plazaId: parseInt(plazaId) };
      
      if (qrType) {
        query += ` AND vqr.QRType = @qrType`;
        queryParams.qrType = qrType;
      }
      
      if (isActive !== undefined) {
        query += ` AND vqr.IsActive = @isActive`;
        queryParams.isActive = isActive === 'true' ? 1 : 0;
      }
      
      query += `
        ORDER BY vqr.CreatedOn DESC
        OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY
      `;
      
      const offset = (pageNumber - 1) * pageSize;
      queryParams.offset = offset;
      queryParams.pageSize = parseInt(pageSize);
      
      result = await db.query(query, queryParams);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as TotalCount
      FROM ValetQRCodes 
      WHERE PlazaId = @plazaId
      ${qrType ? 'AND QRType = @qrType' : ''}
      ${isActive !== undefined ? 'AND IsActive = @isActive' : ''}
    `;
    
    const countParams = { plazaId: parseInt(plazaId) };
    if (qrType) countParams.qrType = qrType;
    if (isActive !== undefined) countParams.isActive = isActive === 'true' ? 1 : 0;
    
    const countResult = await db.query(countQuery, countParams);
    const totalCount = countResult.recordset[0].TotalCount;

    res.json({
      success: true,
      message: 'QR codes retrieved successfully',
      data: result.recordset,
      pagination: {
        currentPage: parseInt(pageNumber),
        pageSize: parseInt(pageSize),
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    });

  } catch (error) {
    console.error('Error in getQRCodesByPlaza:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve QR codes',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Deactivate QR code
exports.deactivateQRCode = async (req, res) => {
  try {
    const { qrCodeId } = req.params;

    if (!qrCodeId) {
      return res.status(400).json({
        success: false,
        message: 'QR code ID is required'
      });
    }

    const updateQuery = `
      UPDATE ValetQRCodes 
      SET IsActive = 0, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
      WHERE Id = @qrCodeId AND IsActive = 1
    `;
    
    const result = await db.query(updateQuery, {
      qrCodeId: parseInt(qrCodeId),
      modifiedBy: req.user?.id || 1
    });

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({
        success: false,
        message: 'QR code not found or already inactive'
      });
    }

    res.json({
      success: true,
      message: 'QR code deactivated successfully'
    });

  } catch (error) {
    console.error('Error in deactivateQRCode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to deactivate QR code',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
