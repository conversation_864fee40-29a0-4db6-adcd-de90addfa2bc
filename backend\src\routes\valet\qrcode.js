const express = require('express');
const router = express.Router();
const QRCodeController = require('../../controllers/valet/QRCodeController');
const { auth, authorizeRoles } = require('../../middleware/auth');

/**
 * QR Code Routes for Valet System
 * All routes require authentication and appropriate role permissions
 */

// Generate QR code for valet point
// POST /api/valet/qrcode/generate
router.post('/generate',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  QRCodeController.generateQRCode
);

// Get QR code by data (for scanning)
// GET /api/valet/qrcode/scan/:qrData
router.get('/scan/:qrData',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController', 'ValetDriver']),
  QRCodeController.getQRCodeByData
);

// Get all QR codes for a plaza
// GET /api/valet/qrcode/plaza/:plazaId
router.get('/plaza/:plazaId',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  QRCodeController.getQRCodesByPlaza
);

// Deactivate QR code
// PUT /api/valet/qrcode/:qrCodeId/deactivate
router.put('/:qrCodeId/deactivate',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  QRCodeController.deactivateQRCode
);

module.exports = router;
