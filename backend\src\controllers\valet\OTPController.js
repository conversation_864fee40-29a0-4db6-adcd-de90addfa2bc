const db = require('../../config/database');
const sql = require('mssql');

/**
 * Valet OTP Controller
 * Handles OTP generation, verification, and management for valet system
 */

// Generate and send OTP
exports.generateOTP = async (req, res) => {
  try {
    const { mobileNumber, otpType = 'REGISTRATION' } = req.body;

    // Validate required fields
    if (!mobileNumber) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number is required'
      });
    }

    // Validate mobile number format
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!mobileRegex.test(mobileNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid 10-digit mobile number'
      });
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Set expiry time (5 minutes from now)
    const expiryMinutes = process.env.VALET_OTP_EXPIRY_MINUTES || 5;
    const expiryTime = new Date();
    expiryTime.setMinutes(expiryTime.getMinutes() + parseInt(expiryMinutes));

    // Deactivate any existing active OTPs for this mobile number
    const deactivateQuery = `
      UPDATE OTP 
      SET IsActive = 0, ModifiedBy = 1, ModifiedOn = GETDATE()
      WHERE MobileNumber = @mobileNumber AND IsActive = 1
    `;
    
    await db.query(deactivateQuery, { mobileNumber });

    // Insert new OTP
    const insertQuery = `
      INSERT INTO OTP (MobileNumber, OTP, ExpireTime, IsActive, CreatedBy, CreatedOn)
      OUTPUT INSERTED.Id
      VALUES (@mobileNumber, @otp, @expiryTime, 1, 1, GETDATE())
    `;
    
    const result = await db.query(insertQuery, { 
      mobileNumber, 
      otp, 
      expiryTime: expiryTime.toISOString()
    });

    // TODO: Send SMS using SMS service
    // For now, we'll just log it (in production, integrate with SMS gateway)
    console.log(`OTP for ${mobileNumber}: ${otp} (Expires: ${expiryTime})`);

    // Insert SMS notification record
    const smsQuery = `
      INSERT INTO SMSNotifications (MobileNumber, Message, SMSType, Status, CreatedOn)
      VALUES (@mobileNumber, @message, @smsType, 'PENDING', GETDATE())
    `;
    
    const smsMessage = `Your Parkwiz valet OTP is: ${otp}. Valid for ${expiryMinutes} minutes. Do not share with anyone.`;
    
    await db.query(smsQuery, {
      mobileNumber,
      message: smsMessage,
      smsType: otpType
    });

    res.json({
      success: true,
      message: 'OTP sent successfully',
      data: {
        otpId: result.recordset[0].Id,
        expiryTime: expiryTime,
        // Don't send OTP in production for security
        ...(process.env.NODE_ENV === 'development' && { otp })
      }
    });

  } catch (error) {
    console.error('Error in generateOTP:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate OTP',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Verify OTP
exports.verifyOTP = async (req, res) => {
  try {
    const { mobileNumber, otp } = req.body;

    // Validate required fields
    if (!mobileNumber || !otp) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number and OTP are required'
      });
    }

    // Find active OTP for this mobile number
    const otpQuery = `
      SELECT Id, OTP, ExpireTime, CreatedOn
      FROM OTP 
      WHERE MobileNumber = @mobileNumber 
        AND IsActive = 1 
        AND ExpireTime > GETDATE()
      ORDER BY CreatedOn DESC
    `;
    
    const otpResult = await db.query(otpQuery, { mobileNumber });
    
    if (otpResult.recordset.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }

    const otpRecord = otpResult.recordset[0];

    // Verify OTP
    if (otpRecord.OTP !== otp) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP'
      });
    }

    // Deactivate the OTP after successful verification
    const deactivateQuery = `
      UPDATE OTP 
      SET IsActive = 0, ModifiedBy = 1, ModifiedOn = GETDATE()
      WHERE Id = @otpId
    `;
    
    await db.query(deactivateQuery, { otpId: otpRecord.Id });

    res.json({
      success: true,
      message: 'OTP verified successfully',
      data: {
        mobileNumber,
        verifiedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error in verifyOTP:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify OTP',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Resend OTP
exports.resendOTP = async (req, res) => {
  try {
    const { mobileNumber } = req.body;

    // Check if there's a recent OTP request (prevent spam)
    const recentOtpQuery = `
      SELECT TOP 1 CreatedOn
      FROM OTP 
      WHERE MobileNumber = @mobileNumber 
      ORDER BY CreatedOn DESC
    `;
    
    const recentOtp = await db.query(recentOtpQuery, { mobileNumber });
    
    if (recentOtp.recordset.length > 0) {
      const lastOtpTime = new Date(recentOtp.recordset[0].CreatedOn);
      const currentTime = new Date();
      const timeDifference = (currentTime - lastOtpTime) / 1000; // in seconds
      
      // Prevent resend if last OTP was sent within 60 seconds
      if (timeDifference < 60) {
        return res.status(429).json({
          success: false,
          message: `Please wait ${Math.ceil(60 - timeDifference)} seconds before requesting a new OTP`
        });
      }
    }

    // Generate new OTP using the same logic as generateOTP
    return exports.generateOTP(req, res);

  } catch (error) {
    console.error('Error in resendOTP:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resend OTP',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get OTP status
exports.getOTPStatus = async (req, res) => {
  try {
    const { mobileNumber } = req.params;

    const otpQuery = `
      SELECT 
        Id,
        ExpireTime,
        CreatedOn,
        CASE 
          WHEN ExpireTime > GETDATE() THEN 'ACTIVE'
          ELSE 'EXPIRED'
        END as Status
      FROM OTP 
      WHERE MobileNumber = @mobileNumber 
        AND IsActive = 1
      ORDER BY CreatedOn DESC
    `;
    
    const result = await db.query(otpQuery, { mobileNumber });
    
    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No active OTP found for this mobile number'
      });
    }

    const otpRecord = result.recordset[0];
    const remainingTime = otpRecord.Status === 'ACTIVE' 
      ? Math.max(0, Math.floor((new Date(otpRecord.ExpireTime) - new Date()) / 1000))
      : 0;

    res.json({
      success: true,
      data: {
        otpId: otpRecord.Id,
        status: otpRecord.Status,
        expiryTime: otpRecord.ExpireTime,
        remainingSeconds: remainingTime,
        createdOn: otpRecord.CreatedOn
      },
      message: 'OTP status retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getOTPStatus:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get OTP status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Clean up expired OTPs (utility function)
exports.cleanupExpiredOTPs = async (req, res) => {
  try {
    const cleanupQuery = `
      UPDATE OTP 
      SET IsActive = 0, ModifiedBy = 1, ModifiedOn = GETDATE()
      WHERE IsActive = 1 AND ExpireTime < GETDATE()
    `;
    
    const result = await db.query(cleanupQuery);
    
    res.json({
      success: true,
      message: `Cleaned up expired OTPs`,
      cleanedCount: result.rowsAffected[0] || 0
    });

  } catch (error) {
    console.error('Error in cleanupExpiredOTPs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup expired OTPs',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get OTP statistics (for admin)
exports.getOTPStatistics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    let query = `
      SELECT 
        COUNT(*) as TotalOTPs,
        COUNT(CASE WHEN IsActive = 1 THEN 1 END) as ActiveOTPs,
        COUNT(CASE WHEN ExpireTime < GETDATE() THEN 1 END) as ExpiredOTPs,
        COUNT(DISTINCT MobileNumber) as UniqueMobileNumbers,
        AVG(DATEDIFF(SECOND, CreatedOn, ModifiedOn)) as AvgVerificationTime
      FROM OTP
      WHERE 1=1
    `;
    
    const queryParams = {};
    
    if (startDate) {
      query += ` AND CreatedOn >= @startDate`;
      queryParams.startDate = startDate;
    }
    
    if (endDate) {
      query += ` AND CreatedOn <= @endDate`;
      queryParams.endDate = endDate;
    }

    const result = await db.query(query, queryParams);

    res.json({
      success: true,
      statistics: result.recordset[0],
      message: 'OTP statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getOTPStatistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get OTP statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
