const express = require('express');
const router = express.Router();
const CustomerFlowController = require('../../controllers/valet/CustomerFlowController');
const { authenticateToken } = require('../../middleware/auth');

/**
 * Valet Customer Flow Routes
 * Manages customer session state, form data persistence, and flow progression
 */

// Initialize customer session
router.post('/initialize', authenticateToken, CustomerFlowController.initializeSession);

// Update customer session with form data
router.put('/update/:sessionId', authenticateToken, CustomerFlowController.updateSession);

// Get current session state
router.get('/session/:sessionId', authenticateToken, CustomerFlowController.getSession);

// Complete customer session (when transaction is created)
router.post('/complete/:sessionId', authenticateToken, CustomerFlowController.completeSession);

// Validate session step progression
router.post('/validate-step', authenticateToken, CustomerFlowController.validateStepProgression);

// Get customer session history
router.get('/history/:customerId', authenticateToken, CustomerFlowController.getSessionHistory);

module.exports = router;
