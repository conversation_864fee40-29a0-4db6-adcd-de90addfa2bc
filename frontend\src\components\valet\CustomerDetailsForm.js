import React, { useState, useEffect } from 'react';
import { User, Car, MapPin, ArrowRight, AlertCircle, Loader2, CheckCircle } from 'lucide-react';
import { valetCustomerApi } from '../../api/valetCustomerApi';
import { useToast } from '../../hooks/useToast';

const CustomerDetailsForm = ({ 
  mobileNumber,
  customerId,
  customerData = null,
  plazaInfo,
  onNext, 
  onBack,
  className = "" 
}) => {
  const [formData, setFormData] = useState({
    guestName: '',
    vehicleNumber: '',
    location: '',
    specialInstructions: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [isValid, setIsValid] = useState(false);
  
  const toast = useToast();

  // Pre-fill form if customer data exists
  useEffect(() => {
    if (customerData) {
      setFormData({
        guestName: customerData.guestName || '',
        vehicleNumber: customerData.vehicleNumber || '',
        location: customerData.location || '',
        specialInstructions: customerData.specialInstructions || ''
      });
    }
  }, [customerData]);

  // Validate form
  useEffect(() => {
    const newErrors = {};
    
    // Guest name validation
    if (!formData.guestName.trim()) {
      newErrors.guestName = 'Guest name is required';
    } else if (formData.guestName.trim().length < 2) {
      newErrors.guestName = 'Guest name must be at least 2 characters';
    }

    // Vehicle number validation
    if (!formData.vehicleNumber.trim()) {
      newErrors.vehicleNumber = 'Vehicle number is required';
    } else {
      // Indian vehicle number format validation
      const vehicleRegex = /^[A-Z]{2}[0-9]{1,2}[A-Z]{1,2}[0-9]{4}$/;
      const cleanVehicle = formData.vehicleNumber.replace(/\s+/g, '').toUpperCase();
      if (!vehicleRegex.test(cleanVehicle)) {
        newErrors.vehicleNumber = 'Please enter a valid vehicle number (e.g., MH12AB1234)';
      }
    }

    // Location validation (optional but if provided should be meaningful)
    if (formData.location.trim() && formData.location.trim().length < 3) {
      newErrors.location = 'Location must be at least 3 characters if provided';
    }

    setErrors(newErrors);
    setIsValid(Object.keys(newErrors).length === 0);
  }, [formData]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleVehicleNumberChange = (value) => {
    // Auto-format vehicle number
    const formatted = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    handleInputChange('vehicleNumber', formatted);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!isValid) {
      toast.error('Please fix the errors before continuing');
      return;
    }

    try {
      setIsLoading(true);

      // Prepare customer registration data
      const registrationData = {
        mobileNumber,
        guestName: formData.guestName.trim(),
        vehicleNumber: formData.vehicleNumber.replace(/\s+/g, '').toUpperCase(),
        location: formData.location.trim(),
        specialInstructions: formData.specialInstructions.trim(),
        plazaId: plazaInfo?.plazaId,
        plazaValetPointId: plazaInfo?.plazaValetPointId
      };

      const response = await valetCustomerApi.registerCustomer(registrationData);
      
      if (response.success) {
        toast.success('Customer details saved successfully!');
        onNext({
          ...registrationData,
          customerId: response.customerId,
          customerData: response.customerData
        });
      } else {
        throw new Error(response.message || 'Failed to save customer details');
      }
      
    } catch (error) {
      console.error('Error saving customer details:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to save customer details';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
      
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <User className="w-8 h-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Customer Details</h2>
        <p className="text-gray-600">
          Please provide your details for valet service
        </p>
      </div>

      {/* Plaza Info */}
      {plazaInfo && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-1">{plazaInfo.plazaName}</h3>
          <p className="text-sm text-blue-700">{plazaInfo.valetPointName}</p>
          <p className="text-xs text-blue-600 mt-1">Mobile: {mobileNumber}</p>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        
        {/* Guest Name */}
        <div>
          <label htmlFor="guestName" className="block text-sm font-medium text-gray-700 mb-2">
            Guest Name *
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="w-5 h-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="guestName"
              value={formData.guestName}
              onChange={(e) => handleInputChange('guestName', e.target.value)}
              placeholder="Enter guest name"
              className={`
                block w-full pl-10 pr-4 py-3 border rounded-lg
                focus:ring-2 focus:ring-green-500 focus:border-green-500
                ${errors.guestName ? 'border-red-300' : 'border-gray-300'}
                ${isLoading ? 'bg-gray-50' : 'bg-white'}
              `}
              disabled={isLoading}
              autoComplete="name"
            />
          </div>
          {errors.guestName && (
            <p className="mt-1 text-sm text-red-600">{errors.guestName}</p>
          )}
        </div>

        {/* Vehicle Number */}
        <div>
          <label htmlFor="vehicleNumber" className="block text-sm font-medium text-gray-700 mb-2">
            Vehicle Number *
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Car className="w-5 h-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="vehicleNumber"
              value={formData.vehicleNumber}
              onChange={(e) => handleVehicleNumberChange(e.target.value)}
              placeholder="MH12AB1234"
              className={`
                block w-full pl-10 pr-4 py-3 border rounded-lg uppercase
                focus:ring-2 focus:ring-green-500 focus:border-green-500
                ${errors.vehicleNumber ? 'border-red-300' : 'border-gray-300'}
                ${isLoading ? 'bg-gray-50' : 'bg-white'}
              `}
              disabled={isLoading}
              maxLength="10"
            />
          </div>
          {errors.vehicleNumber && (
            <p className="mt-1 text-sm text-red-600">{errors.vehicleNumber}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Enter vehicle number without spaces (e.g., MH12AB1234)
          </p>
        </div>

        {/* Location */}
        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
            Location (Optional)
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MapPin className="w-5 h-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="location"
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="e.g., Near main entrance, Parking lot A"
              className={`
                block w-full pl-10 pr-4 py-3 border rounded-lg
                focus:ring-2 focus:ring-green-500 focus:border-green-500
                ${errors.location ? 'border-red-300' : 'border-gray-300'}
                ${isLoading ? 'bg-gray-50' : 'bg-white'}
              `}
              disabled={isLoading}
            />
          </div>
          {errors.location && (
            <p className="mt-1 text-sm text-red-600">{errors.location}</p>
          )}
        </div>

        {/* Special Instructions */}
        <div>
          <label htmlFor="specialInstructions" className="block text-sm font-medium text-gray-700 mb-2">
            Special Instructions (Optional)
          </label>
          <textarea
            id="specialInstructions"
            value={formData.specialInstructions}
            onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
            placeholder="Any special instructions for the valet..."
            rows="3"
            className={`
              block w-full px-4 py-3 border rounded-lg resize-none
              focus:ring-2 focus:ring-green-500 focus:border-green-500
              ${isLoading ? 'bg-gray-50' : 'bg-white'}
            `}
            disabled={isLoading}
            maxLength="200"
          />
          <p className="mt-1 text-xs text-gray-500">
            {formData.specialInstructions.length}/200 characters
          </p>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={!isValid || isLoading}
          className={`
            w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors
            ${isValid && !isLoading
              ? 'bg-green-500 hover:bg-green-600 text-white'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }
          `}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Saving Details...
            </>
          ) : (
            <>
              Continue to Payment
              <ArrowRight className="w-5 h-5 ml-2" />
            </>
          )}
        </button>
      </form>

      {/* Back Button */}
      {onBack && (
        <button
          onClick={onBack}
          disabled={isLoading}
          className="w-full mt-4 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
        >
          ← Back to OTP Verification
        </button>
      )}
    </div>
  );
};

export default CustomerDetailsForm;
