const sql = require('mssql');
require('dotenv').config({path: './backend/.env'});

async function checkValetTables() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    console.log('=== CHECKING VALET-RELATED TABLES ===');
    
    const tablesQuery = `
      SELECT TABLE_NAME, TABLE_SCHEMA
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE' 
      AND (TABLE_NAME LIKE '%valet%' OR TABLE_NAME LIKE '%Valet%')
      ORDER BY TABLE_NAME
    `;
    
    const result = await sql.query(tablesQuery);
    console.log('Valet Tables Found:', result.recordset);
    
    // Check PlazaValetPoint table structure
    if (result.recordset.some(t => t.TABLE_NAME === 'PlazaValetPoint')) {
      console.log('\n=== PlazaValetPoint Table Structure ===');
      const structureQuery = `
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          CHARACTER_MAXIMUM_LENGTH
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'PlazaValetPoint'
        ORDER BY ORDINAL_POSITION
      `;
      
      const structure = await sql.query(structureQuery);
      console.table(structure.recordset);
    }
    
    // Check all tables that might be related to valet system
    console.log('\n=== ALL TABLES IN DATABASE ===');
    const allTablesQuery = `
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `;
    
    const allTables = await sql.query(allTablesQuery);
    console.log('All Tables:', allTables.recordset.map(t => t.TABLE_NAME).join(', '));
    
    await sql.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

checkValetTables();
