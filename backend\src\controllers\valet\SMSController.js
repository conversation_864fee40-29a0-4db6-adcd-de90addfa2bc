const db = require('../../config/database');
const sql = require('mssql');

/**
 * Valet SMS Controller
 * Handles SMS notifications for OTP, VRN, pickup alerts, and other customer notifications
 */

// SMS Message Templates
const SMS_TEMPLATES = {
  OTP_VERIFICATION: "Your one time authentication code is: {OTP}. Valid for 10 minutes. - Parkwiz",
  VRN_NOTIFICATION: "Your vehicle {VEHICLE_NUMBER} has been parked safely. VRN: {VRN}. Track your vehicle: {TRACKING_URL} - Parkwiz",
  PICKUP_READY: "Your vehicle {VEHICLE_NUMBER} is ready for pickup. Click here to request: {PICKUP_URL} - Parkwiz",
  PICKUP_ASSIGNED: "Driver assigned for pickup of {VEHICLE_NUMBER}. ETA: {ETA} minutes. Track: {TRACKING_URL} - Parkwiz",
  PICKUP_COMPLETED: "Vehicle {VEHICLE_NUMBER} has been delivered successfully. Thank you for using Parkwiz Valet Service!",
  PAYMENT_SUCCESS: "Payment of ₹{AMOUNT} successful for vehicle {VEHICLE_NUMBER}. Transaction ID: {TRANSACTION_ID} - Parkwiz",
  CASH_PAYMENT_PENDING: "Please visit valet desk to complete cash payment of ₹{AMOUNT} for vehicle {VEHICLE_NUMBER} - Parkwiz"
};

// Send OTP SMS
exports.sendOTP = async (req, res) => {
  try {
    const { mobileNumber, otp, customerId } = req.body;

    if (!mobileNumber || !otp) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number and OTP are required'
      });
    }

    // Validate mobile number format
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!mobileRegex.test(mobileNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid 10-digit mobile number'
      });
    }

    // Prepare SMS message
    const message = SMS_TEMPLATES.OTP_VERIFICATION.replace('{OTP}', otp);

    try {
      // Try to send SMS using stored procedure
      const result = await db.query(`
        DECLARE @SMSId INT;
        EXEC sp_Valet_SMS_Send
          @MobileNumber = @mobileNumber,
          @Message = @message,
          @SMSType = 'OTP',
          @CustomerId = @customerId,
          @TransactionId = NULL,
          @CreatedBy = @customerId,
          @SMSId = @SMSId OUTPUT;
        SELECT @SMSId as SMSId;
      `, {
        mobileNumber,
        message,
        customerId: customerId || null
      });

      const smsId = result.recordset[0].SMSId;

      // Here you would integrate with actual SMS service (like Twilio, AWS SNS, etc.)
      // For now, we'll simulate SMS sending
      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        // Update SMS status to SENT
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'SENT', SentOn = GETDATE() 
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'OTP sent successfully',
          data: {
            smsId,
            mobileNumber,
            sentOn: new Date(),
            type: 'OTP'
          }
        });
      } else {
        // Update SMS status to FAILED
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'FAILED' 
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send OTP SMS'
        });
      }

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const insertSMSQuery = `
        INSERT INTO SMSNotifications (MobileNumber, Message, SMSType, Status, CreatedOn)
        OUTPUT INSERTED.Id
        VALUES (@mobileNumber, @message, 'OTP', 'PENDING', GETDATE())
      `;

      const smsResult = await db.query(insertSMSQuery, { mobileNumber, message });
      const smsId = smsResult.recordset[0].Id;

      // Simulate SMS sending
      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'SENT', SentOn = GETDATE() 
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'OTP sent successfully',
          data: {
            smsId,
            mobileNumber,
            sentOn: new Date(),
            type: 'OTP'
          }
        });
      } else {
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'FAILED' 
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send OTP SMS'
        });
      }
    }

  } catch (error) {
    console.error('Error in sendOTP:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production' 
        ? 'Failed to send OTP' 
        : error.message
    });
  }
};

// Send VRN notification SMS
exports.sendVRNNotification = async (req, res) => {
  try {
    const { 
      mobileNumber, 
      vehicleNumber, 
      vrn, 
      transactionId, 
      customerId,
      trackingUrl 
    } = req.body;

    if (!mobileNumber || !vehicleNumber || !vrn || !transactionId) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number, vehicle number, VRN, and transaction ID are required'
      });
    }

    // Prepare SMS message
    let message = SMS_TEMPLATES.VRN_NOTIFICATION
      .replace('{VEHICLE_NUMBER}', vehicleNumber)
      .replace('{VRN}', vrn);

    if (trackingUrl) {
      message = message.replace('{TRACKING_URL}', trackingUrl);
    } else {
      message = message.replace('Track your vehicle: {TRACKING_URL}', 'Thank you for using our service.');
    }

    try {
      // Try to send SMS using stored procedure
      const result = await db.query(`
        DECLARE @SMSId INT;
        EXEC sp_Valet_SMS_Send
          @MobileNumber = @mobileNumber,
          @Message = @message,
          @SMSType = 'VRN',
          @CustomerId = @customerId,
          @TransactionId = @transactionId,
          @CreatedBy = @customerId,
          @SMSId = @SMSId OUTPUT;
        SELECT @SMSId as SMSId;
      `, {
        mobileNumber,
        message,
        customerId: customerId || null,
        transactionId
      });

      const smsId = result.recordset[0].SMSId;

      // Send SMS
      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'SENT', SentOn = GETDATE() 
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'VRN notification sent successfully',
          data: {
            smsId,
            mobileNumber,
            vehicleNumber,
            vrn,
            sentOn: new Date(),
            type: 'VRN'
          }
        });
      } else {
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'FAILED' 
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send VRN notification'
        });
      }

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const insertSMSQuery = `
        INSERT INTO SMSNotifications (MobileNumber, Message, SMSType, TransactionId, Status, CreatedOn)
        OUTPUT INSERTED.Id
        VALUES (@mobileNumber, @message, 'VRN', @transactionId, 'PENDING', GETDATE())
      `;

      const smsResult = await db.query(insertSMSQuery, { 
        mobileNumber, 
        message, 
        transactionId 
      });
      const smsId = smsResult.recordset[0].Id;

      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'SENT', SentOn = GETDATE() 
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'VRN notification sent successfully',
          data: {
            smsId,
            mobileNumber,
            vehicleNumber,
            vrn,
            sentOn: new Date(),
            type: 'VRN'
          }
        });
      } else {
        await db.query(`
          UPDATE SMSNotifications 
          SET Status = 'FAILED' 
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send VRN notification'
        });
      }
    }

  } catch (error) {
    console.error('Error in sendVRNNotification:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production' 
        ? 'Failed to send VRN notification' 
        : error.message
    });
  }
};

// Send pickup ready notification SMS
exports.sendPickupReadyNotification = async (req, res) => {
  try {
    const {
      mobileNumber,
      vehicleNumber,
      transactionId,
      customerId,
      pickupUrl
    } = req.body;

    if (!mobileNumber || !vehicleNumber || !transactionId) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number, vehicle number, and transaction ID are required'
      });
    }

    // Prepare SMS message
    let message = SMS_TEMPLATES.PICKUP_READY
      .replace('{VEHICLE_NUMBER}', vehicleNumber);

    if (pickupUrl) {
      message = message.replace('{PICKUP_URL}', pickupUrl);
    } else {
      message = message.replace('Click here to request: {PICKUP_URL}', 'Please visit valet desk to request pickup.');
    }

    try {
      // Try to send SMS using stored procedure
      const result = await db.query(`
        DECLARE @SMSId INT;
        EXEC sp_Valet_SMS_Send
          @MobileNumber = @mobileNumber,
          @Message = @message,
          @SMSType = 'PICKUP_READY',
          @CustomerId = @customerId,
          @TransactionId = @transactionId,
          @CreatedBy = @customerId,
          @SMSId = @SMSId OUTPUT;
        SELECT @SMSId as SMSId;
      `, {
        mobileNumber,
        message,
        customerId: customerId || null,
        transactionId
      });

      const smsId = result.recordset[0].SMSId;
      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'SENT', SentOn = GETDATE()
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'Pickup ready notification sent successfully',
          data: {
            smsId,
            mobileNumber,
            vehicleNumber,
            sentOn: new Date(),
            type: 'PICKUP_READY'
          }
        });
      } else {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'FAILED'
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send pickup ready notification'
        });
      }

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);

      const insertSMSQuery = `
        INSERT INTO SMSNotifications (MobileNumber, Message, SMSType, TransactionId, Status, CreatedOn)
        OUTPUT INSERTED.Id
        VALUES (@mobileNumber, @message, 'PICKUP_READY', @transactionId, 'PENDING', GETDATE())
      `;

      const smsResult = await db.query(insertSMSQuery, {
        mobileNumber,
        message,
        transactionId
      });
      const smsId = smsResult.recordset[0].Id;

      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'SENT', SentOn = GETDATE()
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'Pickup ready notification sent successfully',
          data: {
            smsId,
            mobileNumber,
            vehicleNumber,
            sentOn: new Date(),
            type: 'PICKUP_READY'
          }
        });
      } else {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'FAILED'
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send pickup ready notification'
        });
      }
    }

  } catch (error) {
    console.error('Error in sendPickupReadyNotification:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to send pickup ready notification'
        : error.message
    });
  }
};

// Send payment success notification SMS
exports.sendPaymentSuccessNotification = async (req, res) => {
  try {
    const {
      mobileNumber,
      vehicleNumber,
      amount,
      transactionId,
      customerId,
      gatewayTransactionId
    } = req.body;

    if (!mobileNumber || !vehicleNumber || !amount || !transactionId) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number, vehicle number, amount, and transaction ID are required'
      });
    }

    // Prepare SMS message
    const message = SMS_TEMPLATES.PAYMENT_SUCCESS
      .replace('{AMOUNT}', amount)
      .replace('{VEHICLE_NUMBER}', vehicleNumber)
      .replace('{TRANSACTION_ID}', gatewayTransactionId || transactionId);

    try {
      // Try to send SMS using stored procedure
      const result = await db.query(`
        DECLARE @SMSId INT;
        EXEC sp_Valet_SMS_Send
          @MobileNumber = @mobileNumber,
          @Message = @message,
          @SMSType = 'PAYMENT_SUCCESS',
          @CustomerId = @customerId,
          @TransactionId = @transactionId,
          @CreatedBy = @customerId,
          @SMSId = @SMSId OUTPUT;
        SELECT @SMSId as SMSId;
      `, {
        mobileNumber,
        message,
        customerId: customerId || null,
        transactionId
      });

      const smsId = result.recordset[0].SMSId;
      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'SENT', SentOn = GETDATE()
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'Payment success notification sent successfully',
          data: {
            smsId,
            mobileNumber,
            vehicleNumber,
            amount,
            sentOn: new Date(),
            type: 'PAYMENT_SUCCESS'
          }
        });
      } else {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'FAILED'
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send payment success notification'
        });
      }

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);

      const insertSMSQuery = `
        INSERT INTO SMSNotifications (MobileNumber, Message, SMSType, TransactionId, Status, CreatedOn)
        OUTPUT INSERTED.Id
        VALUES (@mobileNumber, @message, 'PAYMENT_SUCCESS', @transactionId, 'PENDING', GETDATE())
      `;

      const smsResult = await db.query(insertSMSQuery, {
        mobileNumber,
        message,
        transactionId
      });
      const smsId = smsResult.recordset[0].Id;

      const smsDelivered = await simulateSMSDelivery(mobileNumber, message);

      if (smsDelivered) {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'SENT', SentOn = GETDATE()
          WHERE Id = @smsId
        `, { smsId });

        res.json({
          success: true,
          message: 'Payment success notification sent successfully',
          data: {
            smsId,
            mobileNumber,
            vehicleNumber,
            amount,
            sentOn: new Date(),
            type: 'PAYMENT_SUCCESS'
          }
        });
      } else {
        await db.query(`
          UPDATE SMSNotifications
          SET Status = 'FAILED'
          WHERE Id = @smsId
        `, { smsId });

        res.status(500).json({
          success: false,
          message: 'Failed to send payment success notification'
        });
      }
    }

  } catch (error) {
    console.error('Error in sendPaymentSuccessNotification:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to send payment success notification'
        : error.message
    });
  }
};

// Get SMS history for a customer or transaction
exports.getSMSHistory = async (req, res) => {
  try {
    const { customerId, transactionId, mobileNumber } = req.query;
    const { page = 1, limit = 10 } = req.query;

    if (!customerId && !transactionId && !mobileNumber) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID, transaction ID, or mobile number is required'
      });
    }

    const offset = (page - 1) * limit;

    try {
      // Try to get SMS history using stored procedure
      const result = await db.query(`
        EXEC sp_Valet_SMS_GetHistory
          @CustomerId = @customerId,
          @TransactionId = @transactionId,
          @MobileNumber = @mobileNumber,
          @PageNumber = @page,
          @PageSize = @limit
      `, {
        customerId: customerId || null,
        transactionId: transactionId || null,
        mobileNumber: mobileNumber || null,
        page: parseInt(page),
        limit: parseInt(limit)
      });

      const smsHistory = result.recordset;

      res.json({
        success: true,
        message: 'SMS history retrieved successfully',
        data: {
          smsHistory,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: smsHistory.length > 0 ? smsHistory[0].TotalCount || smsHistory.length : 0
          }
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);

      let whereClause = 'WHERE 1=1';
      const params = { offset, limit };

      if (customerId) {
        whereClause += ' AND CustomerId = @customerId';
        params.customerId = customerId;
      }
      if (transactionId) {
        whereClause += ' AND TransactionId = @transactionId';
        params.transactionId = transactionId;
      }
      if (mobileNumber) {
        whereClause += ' AND MobileNumber = @mobileNumber';
        params.mobileNumber = mobileNumber;
      }

      const smsHistoryQuery = `
        SELECT Id, MobileNumber, Message, SMSType, Status, CreatedOn, SentOn, TransactionId
        FROM SMSNotifications
        ${whereClause}
        ORDER BY CreatedOn DESC
        OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
      `;

      const result = await db.query(smsHistoryQuery, params);

      res.json({
        success: true,
        message: 'SMS history retrieved successfully',
        data: {
          smsHistory: result.recordset,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: result.recordset.length
          }
        }
      });
    }

  } catch (error) {
    console.error('Error in getSMSHistory:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to retrieve SMS history'
        : error.message
    });
  }
};

// Simulate SMS delivery (replace with actual SMS service integration)
async function simulateSMSDelivery(mobileNumber, message) {
  try {
    // In production, integrate with SMS service like:
    // - Twilio
    // - AWS SNS
    // - MSG91
    // - TextLocal
    // - etc.

    console.log(`SMS Simulation - Sending to ${mobileNumber}: ${message}`);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulate 95% success rate
    return Math.random() > 0.05;

  } catch (error) {
    console.error('SMS delivery simulation error:', error);
    return false;
  }
}
