import React, { useState, useEffect } from 'react';
import { ArrowLeft, Home } from 'lucide-react';
import QRCodeScanner from './QRCodeScanner';
import MobileNumberEntry from './MobileNumberEntry';
import OTPVerification from './OTPVerification';
import CustomerDetailsForm from './CustomerDetailsForm';
import PaymentGateway from './PaymentGateway';
import TransactionStatus from './TransactionStatus';
import { useToast } from '../../hooks/useToast';

const FLOW_STEPS = {
  QR_SCAN: 'qr_scan',
  MOBILE_ENTRY: 'mobile_entry',
  OTP_VERIFICATION: 'otp_verification',
  CUSTOMER_DETAILS: 'customer_details',
  PAYMENT: 'payment',
  TRANSACTION_STATUS: 'transaction_status'
};

const ValetCustomerFlow = ({ 
  onComplete,
  onCancel,
  initialStep = FLOW_STEPS.QR_SCAN,
  className = "" 
}) => {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [flowData, setFlowData] = useState({
    qrData: null,
    plazaInfo: null,
    mobileNumber: '',
    otpId: null,
    otpVerified: false,
    customerId: null,
    customerData: null,
    transactionId: null,
    paymentData: null
  });
  const [isQRScannerOpen, setIsQRScannerOpen] = useState(initialStep === FLOW_STEPS.QR_SCAN);
  
  const toast = useToast();

  // Handle QR code scan success
  const handleQRScanSuccess = (scanData) => {
    setFlowData(prev => ({
      ...prev,
      qrData: scanData.qrData,
      plazaInfo: scanData.validationResult.plazaInfo
    }));
    setIsQRScannerOpen(false);
    setCurrentStep(FLOW_STEPS.MOBILE_ENTRY);
  };

  // Handle QR code scan error
  const handleQRScanError = (error) => {
    console.error('QR scan error:', error);
    toast.error('Failed to scan QR code. Please try again.');
  };

  // Handle mobile number submission
  const handleMobileNext = (mobileData) => {
    setFlowData(prev => ({
      ...prev,
      mobileNumber: mobileData.mobileNumber,
      otpId: mobileData.otpId,
      expiresAt: mobileData.expiresAt
    }));
    setCurrentStep(FLOW_STEPS.OTP_VERIFICATION);
  };

  // Handle OTP verification success
  const handleOTPNext = (otpData) => {
    setFlowData(prev => ({
      ...prev,
      otpVerified: true,
      customerId: otpData.customerId,
      customerData: otpData.customerData
    }));
    setCurrentStep(FLOW_STEPS.CUSTOMER_DETAILS);
  };

  // Handle OTP resend
  const handleOTPResend = (resendData) => {
    setFlowData(prev => ({
      ...prev,
      otpId: resendData.otpId,
      expiresAt: resendData.expiresAt
    }));
  };

  // Handle customer details submission
  const handleCustomerDetailsNext = (customerData) => {
    setFlowData(prev => ({
      ...prev,
      customerData: {
        ...prev.customerData,
        ...customerData
      },
      customerId: customerData.customerId
    }));
    setCurrentStep(FLOW_STEPS.PAYMENT);
  };

  // Handle payment success
  const handlePaymentSuccess = (paymentData) => {
    setFlowData(prev => ({
      ...prev,
      paymentData,
      transactionId: paymentData.transactionId
    }));
    setCurrentStep(FLOW_STEPS.TRANSACTION_STATUS);
    toast.showSuccess('Payment completed successfully!');
  };

  // Handle payment error
  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    toast.error('Payment failed. Please try again.');
  };

  // Handle step navigation
  const goToStep = (step) => {
    if (step === FLOW_STEPS.QR_SCAN) {
      setIsQRScannerOpen(true);
    }
    setCurrentStep(step);
  };

  const goBack = () => {
    switch (currentStep) {
      case FLOW_STEPS.MOBILE_ENTRY:
        setIsQRScannerOpen(true);
        setCurrentStep(FLOW_STEPS.QR_SCAN);
        break;
      case FLOW_STEPS.OTP_VERIFICATION:
        setCurrentStep(FLOW_STEPS.MOBILE_ENTRY);
        break;
      case FLOW_STEPS.CUSTOMER_DETAILS:
        setCurrentStep(FLOW_STEPS.OTP_VERIFICATION);
        break;
      case FLOW_STEPS.PAYMENT:
        setCurrentStep(FLOW_STEPS.CUSTOMER_DETAILS);
        break;
      default:
        if (onCancel) onCancel();
        break;
    }
  };

  const handleComplete = () => {
    if (onComplete) {
      onComplete(flowData);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case FLOW_STEPS.QR_SCAN:
        return 'Scan QR Code';
      case FLOW_STEPS.MOBILE_ENTRY:
        return 'Enter Mobile Number';
      case FLOW_STEPS.OTP_VERIFICATION:
        return 'Verify OTP';
      case FLOW_STEPS.CUSTOMER_DETAILS:
        return 'Customer Details';
      case FLOW_STEPS.PAYMENT:
        return 'Payment';
      case FLOW_STEPS.TRANSACTION_STATUS:
        return 'Transaction Status';
      default:
        return 'Valet Service';
    }
  };

  const getStepNumber = () => {
    const steps = Object.values(FLOW_STEPS);
    return steps.indexOf(currentStep) + 1;
  };

  const getTotalSteps = () => {
    return Object.values(FLOW_STEPS).length;
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      
      {/* Header */}
      {!isQRScannerOpen && (
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-md mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {currentStep !== FLOW_STEPS.QR_SCAN && (
                  <button
                    onClick={goBack}
                    className="p-2 -ml-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5" />
                  </button>
                )}
                <div className="ml-2">
                  <h1 className="text-lg font-semibold text-gray-900">{getStepTitle()}</h1>
                  <p className="text-sm text-gray-500">
                    Step {getStepNumber()} of {getTotalSteps()}
                  </p>
                </div>
              </div>
              
              {/* Cancel Button */}
              <button
                onClick={handleCancel}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <Home className="w-5 h-5" />
              </button>
            </div>
            
            {/* Progress Bar */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(getStepNumber() / getTotalSteps()) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 p-4">
        
        {/* QR Code Scanner */}
        <QRCodeScanner
          isOpen={isQRScannerOpen}
          onScanSuccess={handleQRScanSuccess}
          onScanError={handleQRScanError}
          onClose={() => {
            setIsQRScannerOpen(false);
            if (currentStep === FLOW_STEPS.QR_SCAN) {
              handleCancel();
            }
          }}
        />

        {/* Mobile Number Entry */}
        {currentStep === FLOW_STEPS.MOBILE_ENTRY && (
          <MobileNumberEntry
            onNext={handleMobileNext}
            onBack={goBack}
            initialMobile={flowData.mobileNumber}
            plazaInfo={flowData.plazaInfo}
          />
        )}

        {/* OTP Verification */}
        {currentStep === FLOW_STEPS.OTP_VERIFICATION && (
          <OTPVerification
            mobileNumber={flowData.mobileNumber}
            otpId={flowData.otpId}
            expiresAt={flowData.expiresAt}
            onNext={handleOTPNext}
            onBack={goBack}
            onResend={handleOTPResend}
          />
        )}

        {/* Customer Details Form */}
        {currentStep === FLOW_STEPS.CUSTOMER_DETAILS && (
          <CustomerDetailsForm
            mobileNumber={flowData.mobileNumber}
            customerId={flowData.customerId}
            customerData={flowData.customerData}
            plazaInfo={flowData.plazaInfo}
            onNext={handleCustomerDetailsNext}
            onBack={goBack}
          />
        )}

        {/* Payment Gateway */}
        {currentStep === FLOW_STEPS.PAYMENT && (
          <PaymentGateway
            customerData={flowData.customerData}
            plazaInfo={flowData.plazaInfo}
            transactionData={flowData}
            onPaymentSuccess={handlePaymentSuccess}
            onPaymentError={handlePaymentError}
            onBack={goBack}
          />
        )}

        {/* Transaction Status */}
        {currentStep === FLOW_STEPS.TRANSACTION_STATUS && (
          <div className="space-y-4">
            <TransactionStatus
              transactionId={flowData.transactionId}
              onRefresh={() => {
                // Refresh transaction status
                console.log('Refreshing transaction status');
              }}
            />
            
            {/* Completion Actions */}
            <div className="max-w-md mx-auto px-6">
              <div className="flex space-x-3">
                <button
                  onClick={() => goToStep(FLOW_STEPS.QR_SCAN)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  New Transaction
                </button>
                <button
                  onClick={handleComplete}
                  className="flex-1 px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white rounded-lg transition-colors"
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ValetCustomerFlow;
