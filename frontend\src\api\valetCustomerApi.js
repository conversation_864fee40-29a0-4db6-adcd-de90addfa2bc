import api from '../services/api';

/**
 * Valet Customer API - Customer-facing valet operations
 * Handles QR code scanning, OTP verification, customer registration, 
 * payment processing, and transaction management
 */
export const valetCustomerApi = {
  
  // =============================================
  // QR CODE OPERATIONS
  // =============================================
  
  /**
   * Validates a QR code and gets valet point information
   * POST /api/valet/qr/validate
   */
  validateQRCode: async (qrData) => {
    try {
      console.log('Validating QR code:', qrData);
      const response = await api.post('/api/valet/qr/validate', { qrData });
      return response.data;
    } catch (error) {
      console.error('Error validating QR code:', error);
      throw error;
    }
  },

  /**
   * Gets QR code information by data
   * GET /api/valet/qr/info/:qrData
   */
  getQRCodeInfo: async (qrData) => {
    try {
      console.log('Getting QR code info:', qrData);
      const response = await api.get(`/api/valet/qr/info/${encodeURIComponent(qrData)}`);
      return response.data;
    } catch (error) {
      console.error('Error getting QR code info:', error);
      throw error;
    }
  },

  // =============================================
  // CUSTOMER OPERATIONS
  // =============================================
  
  /**
   * Registers a new customer or gets existing customer
   * POST /api/valet/customer/register
   */
  registerCustomer: async (customerData) => {
    try {
      console.log('Registering customer:', customerData);
      const response = await api.post('/api/valet/customer/register', customerData);
      return response.data;
    } catch (error) {
      console.error('Error registering customer:', error);
      throw error;
    }
  },

  /**
   * Gets customer by mobile number
   * GET /api/valet/customer/mobile/:mobileNumber
   */
  getCustomerByMobile: async (mobileNumber) => {
    try {
      console.log('Getting customer by mobile:', mobileNumber);
      const response = await api.get(`/api/valet/customer/mobile/${mobileNumber}`);
      return response.data;
    } catch (error) {
      console.error('Error getting customer by mobile:', error);
      throw error;
    }
  },

  // =============================================
  // OTP OPERATIONS
  // =============================================
  
  /**
   * Generates and sends OTP to mobile number
   * POST /api/valet/otp/generate
   */
  generateOTP: async (mobileNumber) => {
    try {
      console.log('Generating OTP for mobile:', mobileNumber);
      const response = await api.post('/api/valet/otp/generate', { mobileNumber });
      return response.data;
    } catch (error) {
      console.error('Error generating OTP:', error);
      throw error;
    }
  },

  /**
   * Verifies OTP for mobile number
   * POST /api/valet/otp/verify
   */
  verifyOTP: async (mobileNumber, otp) => {
    try {
      console.log('Verifying OTP for mobile:', mobileNumber);
      const response = await api.post('/api/valet/otp/verify', { mobileNumber, otp });
      return response.data;
    } catch (error) {
      console.error('Error verifying OTP:', error);
      throw error;
    }
  },

  /**
   * Gets OTP status for mobile number
   * GET /api/valet/otp/status/:mobileNumber
   */
  getOTPStatus: async (mobileNumber) => {
    try {
      console.log('Getting OTP status for mobile:', mobileNumber);
      const response = await api.get(`/api/valet/otp/status/${mobileNumber}`);
      return response.data;
    } catch (error) {
      console.error('Error getting OTP status:', error);
      throw error;
    }
  },

  // =============================================
  // TRANSACTION OPERATIONS
  // =============================================
  
  /**
   * Creates a new valet transaction
   * POST /api/valet/transaction/create
   */
  createTransaction: async (transactionData) => {
    try {
      console.log('Creating valet transaction:', transactionData);
      const response = await api.post('/api/valet/transaction/create', transactionData);
      return response.data;
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw error;
    }
  },

  /**
   * Gets transaction by PNR
   * GET /api/valet/transaction/pnr/:pnr
   */
  getTransactionByPNR: async (pnr) => {
    try {
      console.log('Getting transaction by PNR:', pnr);
      const response = await api.get(`/api/valet/transaction/pnr/${pnr}`);
      return response.data;
    } catch (error) {
      console.error('Error getting transaction by PNR:', error);
      throw error;
    }
  },

  /**
   * Gets transaction by PIN
   * GET /api/valet/transaction/pin/:pin
   */
  getTransactionByPIN: async (pin) => {
    try {
      console.log('Getting transaction by PIN:', pin);
      const response = await api.get(`/api/valet/transaction/pin/${pin}`);
      return response.data;
    } catch (error) {
      console.error('Error getting transaction by PIN:', error);
      throw error;
    }
  },

  /**
   * Updates transaction status
   * PUT /api/valet/transaction/:id/status
   */
  updateTransactionStatus: async (transactionId, status, additionalData = {}) => {
    try {
      console.log('Updating transaction status:', transactionId, status);
      const response = await api.put(`/api/valet/transaction/${transactionId}/status`, {
        status,
        ...additionalData
      });
      return response.data;
    } catch (error) {
      console.error('Error updating transaction status:', error);
      throw error;
    }
  },

  /**
   * Gets customer transactions
   * GET /api/valet/transaction/customer/:customerId
   */
  getCustomerTransactions: async (customerId, params = {}) => {
    try {
      console.log('Getting customer transactions:', customerId);
      const response = await api.get(`/api/valet/transaction/customer/${customerId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error getting customer transactions:', error);
      throw error;
    }
  },

  // =============================================
  // PAYMENT OPERATIONS
  // =============================================
  
  /**
   * Gets payment options for a plaza valet point
   * GET /api/valet/payment/options/:plazaValetPointId
   */
  getPaymentOptions: async (plazaValetPointId) => {
    try {
      console.log('Getting payment options for valet point:', plazaValetPointId);
      const response = await api.get(`/api/valet/payment/options/${plazaValetPointId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting payment options:', error);
      throw error;
    }
  },

  /**
   * Initiates payment for valet transaction
   * POST /api/valet/payment/initiate
   */
  initiatePayment: async (paymentData) => {
    try {
      console.log('Initiating payment:', paymentData);
      const response = await api.post('/api/valet/payment/initiate', paymentData);
      return response.data;
    } catch (error) {
      console.error('Error initiating payment:', error);
      throw error;
    }
  },

  /**
   * Verifies payment status
   * POST /api/valet/payment/verify
   */
  verifyPayment: async (paymentId, transactionId) => {
    try {
      console.log('Verifying payment:', paymentId, transactionId);
      const response = await api.post('/api/valet/payment/verify', {
        paymentId,
        transactionId
      });
      return response.data;
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw error;
    }
  },

  /**
   * Handles payment callback
   * POST /api/valet/payment/callback
   */
  handlePaymentCallback: async (callbackData) => {
    try {
      console.log('Handling payment callback:', callbackData);
      const response = await api.post('/api/valet/payment/callback', callbackData);
      return response.data;
    } catch (error) {
      console.error('Error handling payment callback:', error);
      throw error;
    }
  },

  // =============================================
  // CUSTOMER FLOW OPERATIONS
  // =============================================
  
  /**
   * Starts the complete customer valet flow
   * POST /api/valet/flow/start
   */
  startValetFlow: async (flowData) => {
    try {
      console.log('Starting valet flow:', flowData);
      const response = await api.post('/api/valet/flow/start', flowData);
      return response.data;
    } catch (error) {
      console.error('Error starting valet flow:', error);
      throw error;
    }
  },

  /**
   * Completes customer registration in valet flow
   * POST /api/valet/flow/complete-registration
   */
  completeRegistration: async (registrationData) => {
    try {
      console.log('Completing registration:', registrationData);
      const response = await api.post('/api/valet/flow/complete-registration', registrationData);
      return response.data;
    } catch (error) {
      console.error('Error completing registration:', error);
      throw error;
    }
  },

  /**
   * Processes payment in valet flow
   * POST /api/valet/flow/process-payment
   */
  processPayment: async (paymentData) => {
    try {
      console.log('Processing payment in flow:', paymentData);
      const response = await api.post('/api/valet/flow/process-payment', paymentData);
      return response.data;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  },

  /**
   * Completes valet flow
   * POST /api/valet/flow/complete
   */
  completeValetFlow: async (flowData) => {
    try {
      console.log('Completing valet flow:', flowData);
      const response = await api.post('/api/valet/flow/complete', flowData);
      return response.data;
    } catch (error) {
      console.error('Error completing valet flow:', error);
      throw error;
    }
  }
};

export default valetCustomerApi;
