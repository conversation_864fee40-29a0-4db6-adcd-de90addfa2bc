const db = require('../../config/database');
const sql = require('mssql');

/**
 * Valet Customer Controller
 * Handles customer registration, OTP verification, and customer management for valet system
 */

// Register new customer via mobile number
exports.registerCustomer = async (req, res) => {
  try {
    const { mobileNumber, plazaValetPointId } = req.body;

    // Validate required fields
    if (!mobileNumber || !plazaValetPointId) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number and plaza valet point ID are required'
      });
    }

    // Validate mobile number format (10 digits)
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!mobileRegex.test(mobileNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid 10-digit mobile number'
      });
    }

    // Check if plaza valet point exists and is active
    const valetPointQuery = `
      SELECT pvp.*, p.PlazaName, c.CompanyName 
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON pvp.CompanyId = c.Id
      WHERE pvp.Id = @plazaValetPointId AND pvp.IsActive = 1
    `;
    
    const valetPointResult = await db.query(valetPointQuery, { plazaValetPointId });
    
    if (valetPointResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Valet point not found or inactive'
      });
    }

    const valetPoint = valetPointResult.recordset[0];

    // Check if customer already exists
    let customerId = null;
    const existingCustomerQuery = `
      SELECT Id FROM Customer 
      WHERE MobileNumber = @mobileNumber AND IsActive = 1
    `;
    
    const existingCustomer = await db.query(existingCustomerQuery, { mobileNumber });
    
    if (existingCustomer.recordset.length > 0) {
      customerId = existingCustomer.recordset[0].Id;
    } else {
      // Create new customer
      const createCustomerQuery = `
        INSERT INTO Customer (MobileNumber, IsActive, CreatedBy, CreatedOn)
        OUTPUT INSERTED.Id
        VALUES (@mobileNumber, 1, 1, GETDATE())
      `;
      
      const newCustomer = await db.query(createCustomerQuery, { mobileNumber });
      customerId = newCustomer.recordset[0].Id;
    }

    res.json({
      success: true,
      message: 'Customer registered successfully',
      data: {
        customerId,
        mobileNumber,
        valetPoint: {
          id: valetPoint.Id,
          name: valetPoint.ValetPointName,
          plazaName: valetPoint.PlazaName,
          companyName: valetPoint.CompanyName
        }
      }
    });

  } catch (error) {
    console.error('Error in registerCustomer:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update customer details (name, address, etc.)
exports.updateCustomerDetails = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { name, addressId } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Customer name is required'
      });
    }

    // Check if customer exists
    const customerQuery = `
      SELECT Id FROM Customer 
      WHERE Id = @customerId AND IsActive = 1
    `;
    
    const customer = await db.query(customerQuery, { customerId });
    
    if (customer.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Update customer details
    const updateQuery = `
      UPDATE Customer 
      SET Name = @name, 
          AddressId = @addressId,
          ModifiedBy = 1,
          ModifiedOn = GETDATE()
      WHERE Id = @customerId
    `;
    
    await db.query(updateQuery, { 
      customerId, 
      name: name.trim(), 
      addressId: addressId || null 
    });

    res.json({
      success: true,
      message: 'Customer details updated successfully'
    });

  } catch (error) {
    console.error('Error in updateCustomerDetails:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get customer by mobile number
exports.getCustomerByMobile = async (req, res) => {
  try {
    const { mobileNumber } = req.params;

    const customerQuery = `
      SELECT 
        c.Id,
        c.Name,
        c.MobileNumber,
        c.AddressId,
        c.IsActive,
        c.CreatedOn,
        COUNT(cv.Id) as VehicleCount
      FROM Customer c
      LEFT JOIN CustomerVehicle cv ON c.Id = cv.CustomerId AND cv.IsActive = 1
      WHERE c.MobileNumber = @mobileNumber AND c.IsActive = 1
      GROUP BY c.Id, c.Name, c.MobileNumber, c.AddressId, c.IsActive, c.CreatedOn
    `;
    
    const result = await db.query(customerQuery, { mobileNumber });
    
    if (result.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    res.json({
      success: true,
      customer: result.recordset[0],
      message: 'Customer retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getCustomerByMobile:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get customer vehicles
exports.getCustomerVehicles = async (req, res) => {
  try {
    const { customerId } = req.params;

    const vehiclesQuery = `
      SELECT 
        cv.Id,
        cv.VehicleNumber,
        cv.IsActive,
        cv.CreatedOn,
        COUNT(pt.Id) as TransactionCount
      FROM CustomerVehicle cv
      LEFT JOIN ParkingTransactions pt ON cv.VehicleNumber = pt.CustomerVehicleNumber
      WHERE cv.CustomerId = @customerId AND cv.IsActive = 1
      GROUP BY cv.Id, cv.VehicleNumber, cv.IsActive, cv.CreatedOn
      ORDER BY cv.CreatedOn DESC
    `;
    
    const result = await db.query(vehiclesQuery, { customerId });

    res.json({
      success: true,
      vehicles: result.recordset,
      message: 'Customer vehicles retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getCustomerVehicles:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add customer vehicle
exports.addCustomerVehicle = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { vehicleNumber } = req.body;

    // Validate required fields
    if (!vehicleNumber) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle number is required'
      });
    }

    // Validate vehicle number format (basic validation)
    const vehicleRegex = /^[A-Z]{2}[0-9]{1,2}[A-Z]{1,2}[0-9]{4}$/;
    if (!vehicleRegex.test(vehicleNumber.replace(/\s/g, '').toUpperCase())) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid vehicle number (e.g., MH12AB1234)'
      });
    }

    const formattedVehicleNumber = vehicleNumber.replace(/\s/g, '').toUpperCase();

    // Check if customer exists
    const customerQuery = `
      SELECT Id FROM Customer 
      WHERE Id = @customerId AND IsActive = 1
    `;
    
    const customer = await db.query(customerQuery, { customerId });
    
    if (customer.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Check if vehicle already exists for this customer
    const existingVehicleQuery = `
      SELECT Id FROM CustomerVehicle 
      WHERE CustomerId = @customerId AND VehicleNumber = @vehicleNumber AND IsActive = 1
    `;
    
    const existingVehicle = await db.query(existingVehicleQuery, { 
      customerId, 
      vehicleNumber: formattedVehicleNumber 
    });
    
    if (existingVehicle.recordset.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Vehicle already registered for this customer'
      });
    }

    // Add new vehicle
    const insertQuery = `
      INSERT INTO CustomerVehicle (CustomerId, VehicleNumber, IsActive, CreatedBy, CreatedOn)
      OUTPUT INSERTED.Id
      VALUES (@customerId, @vehicleNumber, 1, 1, GETDATE())
    `;
    
    const result = await db.query(insertQuery, { 
      customerId, 
      vehicleNumber: formattedVehicleNumber 
    });

    res.json({
      success: true,
      message: 'Vehicle added successfully',
      vehicleId: result.recordset[0].Id
    });

  } catch (error) {
    console.error('Error in addCustomerVehicle:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all customers (for management)
exports.getAllCustomers = async (req, res) => {
  try {
    const { 
      pageNumber = 1, 
      pageSize = 50, 
      searchTerm,
      companyId,
      plazaId 
    } = req.query;

    let query = `
      SELECT 
        c.Id,
        c.Name,
        c.MobileNumber,
        c.IsActive,
        c.CreatedOn,
        COUNT(DISTINCT cv.Id) as VehicleCount,
        COUNT(DISTINCT pt.Id) as TransactionCount
      FROM Customer c
      LEFT JOIN CustomerVehicle cv ON c.Id = cv.CustomerId AND cv.IsActive = 1
      LEFT JOIN ParkingTransactions pt ON cv.VehicleNumber = pt.CustomerVehicleNumber
      WHERE c.IsActive = 1
    `;
    
    const queryParams = {};

    // Apply search filter
    if (searchTerm) {
      query += ` AND (c.Name LIKE @searchTerm OR c.MobileNumber LIKE @searchTerm)`;
      queryParams.searchTerm = `%${searchTerm}%`;
    }

    // Apply role-based filtering
    if (req.user && req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin' && companyId) {
        query += ` AND EXISTS (
          SELECT 1 FROM UserCompany uc 
          WHERE uc.UserId = @userId AND uc.CompanyId = @companyId AND uc.IsActive = 1
        )`;
        queryParams.userId = req.user.id;
        queryParams.companyId = companyId;
      } else if (req.user.role === 'PlazaManager' && plazaId) {
        query += ` AND EXISTS (
          SELECT 1 FROM UserPlaza up 
          WHERE up.UserId = @userId AND up.PlazaId = @plazaId AND up.IsActive = 1
        )`;
        queryParams.userId = req.user.id;
        queryParams.plazaId = plazaId;
      }
    }

    query += `
      GROUP BY c.Id, c.Name, c.MobileNumber, c.IsActive, c.CreatedOn
      ORDER BY c.CreatedOn DESC
      OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY
    `;

    const offset = (pageNumber - 1) * pageSize;
    queryParams.offset = offset;
    queryParams.pageSize = parseInt(pageSize);

    const result = await db.query(query, queryParams);

    // Get total count
    let countQuery = `
      SELECT COUNT(DISTINCT c.Id) as TotalCount
      FROM Customer c
      WHERE c.IsActive = 1
    `;
    
    if (searchTerm) {
      countQuery += ` AND (c.Name LIKE @searchTerm OR c.MobileNumber LIKE @searchTerm)`;
    }

    const countResult = await db.query(countQuery, { searchTerm: queryParams.searchTerm });
    const totalCount = countResult.recordset[0].TotalCount;

    res.json({
      success: true,
      customers: result.recordset,
      pagination: {
        currentPage: parseInt(pageNumber),
        pageSize: parseInt(pageSize),
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      },
      message: 'Customers retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getAllCustomers:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
