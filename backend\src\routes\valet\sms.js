const express = require('express');
const router = express.Router();
const SMSController = require('../../controllers/valet/SMSController');
const { authenticateToken } = require('../../middleware/auth');

/**
 * Valet SMS Routes
 * Handles SMS notifications for OTP, VRN, pickup alerts, and other customer notifications
 */

// Send OTP SMS
router.post('/send-otp', authenticateToken, SMSController.sendOTP);

// Send VRN notification SMS
router.post('/send-vrn-notification', authenticateToken, SMSController.sendVRNNotification);

// Send pickup ready notification SMS
router.post('/send-pickup-ready', authenticateToken, SMSController.sendPickupReadyNotification);

// Send payment success notification SMS
router.post('/send-payment-success', authenticateToken, SMSController.sendPaymentSuccessNotification);

// Get SMS history for a customer or transaction
router.get('/history', authenticateToken, SMSController.getSMSHistory);

module.exports = router;
